{"version": 3, "sources": ["../../../src/build/static-paths/app.ts"], "sourcesContent": ["import type { Params } from '../../server/request/params'\nimport type { AppPageModule } from '../../server/route-modules/app-page/module'\nimport type { AppSegment } from '../segment-config/app/app-segments'\nimport type { PrerenderedRoute, StaticPathsResult } from './types'\n\nimport path from 'node:path'\nimport { AfterRunner } from '../../server/after/run-with-after'\nimport { createWorkStore } from '../../server/async-storage/work-store'\nimport { FallbackMode } from '../../lib/fallback'\nimport { getRouteMatcher } from '../../shared/lib/router/utils/route-matcher'\nimport {\n  getRouteRegex,\n  type RouteRegex,\n} from '../../shared/lib/router/utils/route-regex'\nimport type { IncrementalCache } from '../../server/lib/incremental-cache'\nimport { normalizePathname, encodeParam } from './utils'\nimport escapePathDelimiters from '../../shared/lib/router/utils/escape-path-delimiters'\nimport { createIncrementalCache } from '../../export/helpers/create-incremental-cache'\nimport type { NextConfigComplete } from '../../server/config-shared'\n\n/**\n * Filters out duplicate parameters from a list of parameters.\n * This function uses a Map to efficiently store and retrieve unique parameter combinations.\n *\n * @param routeParamKeys - The keys of the parameters. These should be sorted to ensure consistent key generation.\n * @param routeParams - The list of parameter objects to filter.\n * @returns A new array containing only the unique parameter combinations.\n */\nexport function filterUniqueParams(\n  routeParamKeys: readonly string[],\n  routeParams: readonly Params[]\n): Params[] {\n  // A Map is used to store unique parameter combinations. The key of the Map\n  // is a string representation of the parameter combination, and the value\n  // is the actual `Params` object.\n  const unique = new Map<string, Params>()\n\n  // Iterate over each parameter object in the input array.\n  for (const params of routeParams) {\n    let key = '' // Initialize an empty string to build the unique key for the current `params` object.\n\n    // Iterate through the `routeParamKeys` (which are assumed to be sorted).\n    // This consistent order is crucial for generating a stable and unique key\n    // for each parameter combination.\n    for (const paramKey of routeParamKeys) {\n      const value = params[paramKey]\n\n      // Construct a part of the key using the parameter key and its value.\n      // A type prefix (`A:` for Array, `S:` for String, `U:` for undefined) is added to the value\n      // to prevent collisions. For example, `['a', 'b']` and `'a,b'` would\n      // otherwise generate the same string representation, leading to incorrect\n      // deduplication. This ensures that different types with the same string\n      // representation are treated as distinct.\n      let valuePart: string\n      if (Array.isArray(value)) {\n        valuePart = `A:${value.join(',')}`\n      } else if (value === undefined) {\n        valuePart = `U:undefined`\n      } else {\n        valuePart = `S:${value}`\n      }\n      key += `${paramKey}:${valuePart}|`\n    }\n\n    // If the generated key is not already in the `unique` Map, it means this\n    // parameter combination is unique so far. Add it to the Map.\n    if (!unique.has(key)) {\n      unique.set(key, params)\n    }\n  }\n\n  // Convert the Map's values (the unique `Params` objects) back into an array\n  // and return it.\n  return Array.from(unique.values())\n}\n\n/**\n * Filters out all combinations of root params from a list of parameters.\n * This function extracts only the root parameters from each parameter object\n * and then filters out duplicate combinations using a Map for efficiency.\n *\n * Given the following root param ('lang'), and the following routeParams:\n *\n * ```\n * [\n *   { lang: 'en', region: 'US', slug: ['home'] },\n *   { lang: 'en', region: 'US', slug: ['about'] },\n *   { lang: 'fr', region: 'CA', slug: ['about'] },\n * ]\n * ```\n *\n * The result will be:\n *\n * ```\n * [\n *   { lang: 'en', region: 'US' },\n *   { lang: 'fr', region: 'CA' },\n * ]\n * ```\n *\n * @param rootParamKeys - The keys of the root params. These should be sorted\n *   to ensure consistent key generation for the internal Map.\n * @param routeParams - The list of parameter objects to filter.\n * @returns A new array containing only the unique combinations of root params.\n */\nexport function filterUniqueRootParamsCombinations(\n  rootParamKeys: readonly string[],\n  routeParams: readonly Params[]\n): Params[] {\n  // A Map is used to store unique combinations of root parameters.\n  // The key of the Map is a string representation of the root parameter\n  // combination, and the value is the `Params` object containing only\n  // the root parameters.\n  const combinations = new Map<string, Params>()\n\n  // Iterate over each parameter object in the input array.\n  for (const params of routeParams) {\n    const combination: Params = {} // Initialize an object to hold only the root parameters.\n    let key = '' // Initialize an empty string to build the unique key for the current root parameter combination.\n\n    // Iterate through the `rootParamKeys` (which are assumed to be sorted).\n    // This consistent order is crucial for generating a stable and unique key\n    // for each root parameter combination.\n    for (const rootKey of rootParamKeys) {\n      const value = params[rootKey]\n      combination[rootKey] = value // Add the root parameter and its value to the combination object.\n\n      // Construct a part of the key using the root parameter key and its value.\n      // A type prefix (`A:` for Array, `S:` for String, `U:` for undefined) is added to the value\n      // to prevent collisions. This ensures that different types with the same\n      // string representation are treated as distinct.\n      let valuePart: string\n      if (Array.isArray(value)) {\n        valuePart = `A:${value.join(',')}`\n      } else if (value === undefined) {\n        valuePart = `U:undefined`\n      } else {\n        valuePart = `S:${value}`\n      }\n      key += `${rootKey}:${valuePart}|`\n    }\n\n    // If the generated key is not already in the `combinations` Map, it means\n    // this root parameter combination is unique so far. Add it to the Map.\n    if (!combinations.has(key)) {\n      combinations.set(key, combination)\n    }\n  }\n\n  // Convert the Map's values (the unique root parameter `Params` objects)\n  // back into an array and return it.\n  return Array.from(combinations.values())\n}\n\n/**\n * Validates the parameters to ensure they're accessible and have the correct\n * types.\n *\n * @param page - The page to validate.\n * @param regex - The route regex.\n * @param isRoutePPREnabled - Whether the route has partial prerendering enabled.\n * @param routeParamKeys - The keys of the parameters.\n * @param rootParamKeys - The keys of the root params.\n * @param routeParams - The list of parameters to validate.\n * @returns The list of validated parameters.\n */\nfunction validateParams(\n  page: string,\n  regex: RouteRegex,\n  isRoutePPREnabled: boolean,\n  routeParamKeys: readonly string[],\n  rootParamKeys: readonly string[],\n  routeParams: readonly Params[]\n): Params[] {\n  const valid: Params[] = []\n\n  // Validate that if there are any root params, that the user has provided at\n  // least one value for them only if we're using partial prerendering.\n  if (isRoutePPREnabled && rootParamKeys.length > 0) {\n    if (\n      routeParams.length === 0 ||\n      rootParamKeys.some((key) =>\n        routeParams.some((params) => !(key in params))\n      )\n    ) {\n      if (rootParamKeys.length === 1) {\n        throw new Error(\n          `A required root parameter (${rootParamKeys[0]}) was not provided in generateStaticParams for ${page}, please provide at least one value.`\n        )\n      }\n\n      throw new Error(\n        `Required root params (${rootParamKeys.join(', ')}) were not provided in generateStaticParams for ${page}, please provide at least one value for each.`\n      )\n    }\n  }\n\n  for (const params of routeParams) {\n    const item: Params = {}\n\n    for (const key of routeParamKeys) {\n      const { repeat, optional } = regex.groups[key]\n\n      let paramValue = params[key]\n\n      if (\n        optional &&\n        params.hasOwnProperty(key) &&\n        (paramValue === null ||\n          paramValue === undefined ||\n          (paramValue as any) === false)\n      ) {\n        paramValue = []\n      }\n\n      // A parameter is missing, so the rest of the params are not accessible.\n      // We only support this when the route has partial prerendering enabled.\n      // This will make it so that the remaining params are marked as missing so\n      // we can generate a fallback route for them.\n      if (!paramValue && isRoutePPREnabled) {\n        break\n      }\n\n      // Perform validation for the parameter based on whether it's a repeat\n      // parameter or not.\n      if (repeat) {\n        if (!Array.isArray(paramValue)) {\n          throw new Error(\n            `A required parameter (${key}) was not provided as an array received ${typeof paramValue} in generateStaticParams for ${page}`\n          )\n        }\n      } else {\n        if (typeof paramValue !== 'string') {\n          throw new Error(\n            `A required parameter (${key}) was not provided as a string received ${typeof paramValue} in generateStaticParams for ${page}`\n          )\n        }\n      }\n\n      item[key] = paramValue\n    }\n\n    valid.push(item)\n  }\n\n  return valid\n}\n\ninterface TrieNode {\n  /**\n   * The children of the node. Each key is a unique string representation of a parameter value,\n   * and the value is the next TrieNode in the path.\n   */\n  children: Map<string, TrieNode>\n\n  /**\n   * The routes that are associated with this specific parameter combination (node).\n   * These are the routes whose concrete parameters lead to this node in the Trie.\n   */\n  routes: PrerenderedRoute[]\n}\n\n/**\n * Assigns the throwOnEmptyStaticShell property to each of the prerendered routes.\n * This function uses a Trie data structure to efficiently determine whether each route\n * should throw an error when its static shell is empty.\n *\n * A route should not throw on empty static shell if it has child routes in the Trie. For example,\n * if we have two routes, `/blog/first-post` and `/blog/[slug]`, the route for\n * `/blog/[slug]` should not throw because `/blog/first-post` is a more specific concrete route.\n *\n * @param prerenderedRoutes - The prerendered routes.\n * @param routeParamKeys - The keys of the route parameters.\n */\nexport function assignErrorIfEmpty(\n  prerenderedRoutes: readonly PrerenderedRoute[],\n  routeParamKeys: readonly string[]\n): void {\n  // If there are no routes to process, exit early.\n  if (prerenderedRoutes.length === 0) {\n    return\n  }\n\n  // Initialize the root of the Trie. This node represents the starting point\n  // before any parameters have been considered.\n  const root: TrieNode = { children: new Map(), routes: [] }\n\n  // Phase 1: Build the Trie.\n  // Iterate over each prerendered route and insert it into the Trie.\n  // Each route's concrete parameter values form a path in the Trie.\n  for (const route of prerenderedRoutes) {\n    let currentNode = root // Start building the path from the root for each route.\n\n    // Iterate through the sorted parameter keys. The order of keys is crucial\n    // for ensuring that routes with the same concrete parameters follow the\n    // same path in the Trie, regardless of the original order of properties\n    // in the `params` object.\n    for (const key of routeParamKeys) {\n      // Check if the current route actually has a concrete value for this parameter.\n      // If a dynamic segment is not filled (i.e., it's a fallback), it won't have\n      // this property, and we stop building the path for this route at this point.\n      if (route.params.hasOwnProperty(key)) {\n        const value = route.params[key]\n\n        // Generate a unique key for the parameter's value. This is critical\n        // to prevent collisions between different data types that might have\n        // the same string representation (e.g., `['a', 'b']` vs `'a,b'`).\n        // A type prefix (`A:` for Array, `S:` for String, `U:` for undefined)\n        // is added to the value to prevent collisions. This ensures that\n        // different types with the same string representation are treated as\n        // distinct.\n        let valueKey: string\n        if (Array.isArray(value)) {\n          valueKey = `A:${value.join(',')}`\n        } else if (value === undefined) {\n          valueKey = `U:undefined`\n        } else {\n          valueKey = `S:${value}`\n        }\n\n        // Look for a child node corresponding to this `valueKey` from the `currentNode`.\n        let childNode = currentNode.children.get(valueKey)\n        if (!childNode) {\n          // If the child node doesn't exist, create a new one and add it to\n          // the current node's children.\n          childNode = { children: new Map(), routes: [] }\n          currentNode.children.set(valueKey, childNode)\n        }\n        // Move deeper into the Trie to the `childNode` for the next parameter.\n        currentNode = childNode\n      }\n    }\n    // After processing all concrete parameters for the route, add the full\n    // `PrerenderedRoute` object to the `routes` array of the `currentNode`.\n    // This node represents the unique concrete parameter combination for this route.\n    currentNode.routes.push(route)\n  }\n\n  // Phase 2: Traverse the Trie to assign the `throwOnEmptyStaticShell` property.\n  // This is done using an iterative Depth-First Search (DFS) approach with an\n  // explicit stack to avoid JavaScript's recursion depth limits (stack overflow)\n  // for very deep routing structures.\n  const stack: TrieNode[] = [root] // Initialize the stack with the root node.\n\n  while (stack.length > 0) {\n    const node = stack.pop()! // Pop the next node to process from the stack.\n\n    // `hasChildren` indicates if this node has any more specific concrete\n    // parameter combinations branching off from it. If true, it means this\n    // node represents a prefix for other, more specific routes.\n    const hasChildren = node.children.size > 0\n\n    // If the current node has routes associated with it (meaning, routes whose\n    // concrete parameters lead to this node's path in the Trie).\n    if (node.routes.length > 0) {\n      // Determine the minimum number of fallback parameters among all routes\n      // that are associated with this current Trie node. This is used to\n      // identify if a route should not throw on empty static shell relative to another route *at the same level*\n      // of concrete parameters, but with fewer fallback parameters.\n      let minFallbacks = Infinity\n      for (const r of node.routes) {\n        // `fallbackRouteParams?.length ?? 0` handles cases where `fallbackRouteParams`\n        // might be `undefined` or `null`, treating them as 0 length.\n        minFallbacks = Math.min(\n          minFallbacks,\n          r.fallbackRouteParams?.length ?? 0\n        )\n      }\n\n      // Now, for each `PrerenderedRoute` associated with this node:\n      for (const route of node.routes) {\n        // A route is ok not to throw on an empty static shell (and thus\n        // `throwOnEmptyStaticShell` should be `false`) if either of the\n        // following conditions is met:\n        // 1. `hasChildren` is true: This node has further concrete parameter children.\n        //    This means the current route is a parent to more specific routes (e.g.,\n        //    `/blog/[slug]` should not throw when concrete routes like `/blog/first-post` exist).\n        // OR\n        // 2. `route.fallbackRouteParams.length > minFallbacks`: This route has\n        //    more fallback parameters than another route at the same Trie node.\n        //    This implies the current route is a more general version that should not throw\n        //    compared to a more specific route that has fewer fallback parameters\n        //    (e.g., `/1234/[...slug]` should not throw relative to `/[id]/[...slug]`).\n        if (\n          hasChildren ||\n          (route.fallbackRouteParams &&\n            route.fallbackRouteParams.length > minFallbacks)\n        ) {\n          route.throwOnEmptyStaticShell = false // Should not throw on empty static shell.\n        } else {\n          route.throwOnEmptyStaticShell = true // Should throw on empty static shell.\n        }\n      }\n    }\n\n    // Add all children of the current node to the stack. This ensures that\n    // the traversal continues to explore deeper paths in the Trie.\n    for (const child of node.children.values()) {\n      stack.push(child)\n    }\n  }\n}\n\n/**\n * Builds the static paths for an app using `generateStaticParams`.\n *\n * @param params - The parameters for the build.\n * @returns The static paths.\n */\nexport async function buildAppStaticPaths({\n  dir,\n  page,\n  distDir,\n  dynamicIO,\n  authInterrupts,\n  segments,\n  isrFlushToDisk,\n  cacheHandler,\n  cacheLifeProfiles,\n  requestHeaders,\n  cacheHandlers,\n  maxMemoryCacheSize,\n  fetchCacheKeyPrefix,\n  nextConfigOutput,\n  ComponentMod,\n  isRoutePPREnabled = false,\n  buildId,\n  rootParamKeys,\n}: {\n  dir: string\n  page: string\n  dynamicIO: boolean\n  authInterrupts: boolean\n  segments: AppSegment[]\n  distDir: string\n  isrFlushToDisk?: boolean\n  fetchCacheKeyPrefix?: string\n  cacheHandler?: string\n  cacheHandlers?: NextConfigComplete['experimental']['cacheHandlers']\n  cacheLifeProfiles?: {\n    [profile: string]: import('../../server/use-cache/cache-life').CacheLife\n  }\n  maxMemoryCacheSize?: number\n  requestHeaders: IncrementalCache['requestHeaders']\n  nextConfigOutput: 'standalone' | 'export' | undefined\n  ComponentMod: AppPageModule\n  isRoutePPREnabled: boolean\n  buildId: string\n  rootParamKeys: readonly string[]\n}): Promise<StaticPathsResult> {\n  if (\n    segments.some((generate) => generate.config?.dynamicParams === true) &&\n    nextConfigOutput === 'export'\n  ) {\n    throw new Error(\n      '\"dynamicParams: true\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/app/building-your-application/deploying/static-exports'\n    )\n  }\n\n  ComponentMod.patchFetch()\n\n  const incrementalCache = await createIncrementalCache({\n    dir,\n    distDir,\n    cacheHandler,\n    cacheHandlers,\n    requestHeaders,\n    fetchCacheKeyPrefix,\n    flushToDisk: isrFlushToDisk,\n    cacheMaxMemorySize: maxMemoryCacheSize,\n  })\n\n  const regex = getRouteRegex(page)\n  const routeParamKeys = Object.keys(getRouteMatcher(regex)(page) || {})\n\n  const afterRunner = new AfterRunner()\n\n  const store = createWorkStore({\n    page,\n    // We're discovering the parameters here, so we don't have any unknown\n    // ones.\n    fallbackRouteParams: null,\n    renderOpts: {\n      incrementalCache,\n      cacheLifeProfiles,\n      supportsDynamicResponse: true,\n      isRevalidate: false,\n      experimental: {\n        dynamicIO,\n        authInterrupts,\n      },\n      waitUntil: afterRunner.context.waitUntil,\n      onClose: afterRunner.context.onClose,\n      onAfterTaskError: afterRunner.context.onTaskError,\n    },\n    buildId,\n    previouslyRevalidatedTags: [],\n  })\n\n  const routeParams = await ComponentMod.workAsyncStorage.run(\n    store,\n    async () => {\n      async function builtRouteParams(\n        parentsParams: Params[] = [],\n        idx = 0\n      ): Promise<Params[]> {\n        // If we don't have any more to process, then we're done.\n        if (idx === segments.length) return parentsParams\n\n        const current = segments[idx]\n\n        if (\n          typeof current.generateStaticParams !== 'function' &&\n          idx < segments.length\n        ) {\n          return builtRouteParams(parentsParams, idx + 1)\n        }\n\n        const params: Params[] = []\n\n        if (current.generateStaticParams) {\n          // fetchCache can be used to inform the fetch() defaults used inside\n          // of generateStaticParams. revalidate and dynamic options don't come into\n          // play within generateStaticParams.\n          if (typeof current.config?.fetchCache !== 'undefined') {\n            store.fetchCache = current.config.fetchCache\n          }\n\n          if (parentsParams.length > 0) {\n            for (const parentParams of parentsParams) {\n              const result = await current.generateStaticParams({\n                params: parentParams,\n              })\n\n              for (const item of result) {\n                params.push({ ...parentParams, ...item })\n              }\n            }\n          } else {\n            const result = await current.generateStaticParams({ params: {} })\n\n            params.push(...result)\n          }\n        }\n\n        if (idx < segments.length) {\n          return builtRouteParams(params, idx + 1)\n        }\n\n        return params\n      }\n\n      return builtRouteParams()\n    }\n  )\n\n  await afterRunner.executeAfter()\n\n  let lastDynamicSegmentHadGenerateStaticParams = false\n  for (const segment of segments) {\n    // Check to see if there are any missing params for segments that have\n    // dynamicParams set to false.\n    if (\n      segment.param &&\n      segment.isDynamicSegment &&\n      segment.config?.dynamicParams === false\n    ) {\n      for (const params of routeParams) {\n        if (segment.param in params) continue\n\n        const relative = segment.filePath\n          ? path.relative(dir, segment.filePath)\n          : undefined\n\n        throw new Error(\n          `Segment \"${relative}\" exports \"dynamicParams: false\" but the param \"${segment.param}\" is missing from the generated route params.`\n        )\n      }\n    }\n\n    if (\n      segment.isDynamicSegment &&\n      typeof segment.generateStaticParams !== 'function'\n    ) {\n      lastDynamicSegmentHadGenerateStaticParams = false\n    } else if (typeof segment.generateStaticParams === 'function') {\n      lastDynamicSegmentHadGenerateStaticParams = true\n    }\n  }\n\n  // Determine if all the segments have had their parameters provided.\n  const hadAllParamsGenerated =\n    routeParamKeys.length === 0 ||\n    (routeParams.length > 0 &&\n      routeParams.every((params) => {\n        for (const key of routeParamKeys) {\n          if (key in params) continue\n          return false\n        }\n        return true\n      }))\n\n  // TODO: dynamic params should be allowed to be granular per segment but\n  // we need additional information stored/leveraged in the prerender\n  // manifest to allow this behavior.\n  const dynamicParams = segments.every(\n    (segment) => segment.config?.dynamicParams !== false\n  )\n\n  const supportsRoutePreGeneration =\n    hadAllParamsGenerated || process.env.NODE_ENV === 'production'\n\n  const fallbackMode = dynamicParams\n    ? supportsRoutePreGeneration\n      ? isRoutePPREnabled\n        ? FallbackMode.PRERENDER\n        : FallbackMode.BLOCKING_STATIC_RENDER\n      : undefined\n    : FallbackMode.NOT_FOUND\n\n  const prerenderedRoutesByPathname = new Map<string, PrerenderedRoute>()\n\n  if (hadAllParamsGenerated || isRoutePPREnabled) {\n    if (isRoutePPREnabled) {\n      // Discover all unique combinations of the rootParams so we can generate\n      // routes that won't throw on empty static shell for each of them if they're available.\n      routeParams.unshift(\n        ...filterUniqueRootParamsCombinations(rootParamKeys, routeParams)\n      )\n\n      prerenderedRoutesByPathname.set(page, {\n        params: {},\n        pathname: page,\n        encodedPathname: page,\n        fallbackRouteParams: routeParamKeys,\n        fallbackMode: dynamicParams\n          ? // If the fallback params includes any root params, then we need to\n            // perform a blocking static render.\n            rootParamKeys.length > 0\n            ? FallbackMode.BLOCKING_STATIC_RENDER\n            : fallbackMode\n          : FallbackMode.NOT_FOUND,\n        fallbackRootParams: rootParamKeys,\n        // This is set later after all the routes have been processed.\n        throwOnEmptyStaticShell: true,\n      })\n    }\n\n    filterUniqueParams(\n      routeParamKeys,\n      validateParams(\n        page,\n        regex,\n        isRoutePPREnabled,\n        routeParamKeys,\n        rootParamKeys,\n        routeParams\n      )\n    ).forEach((params) => {\n      let pathname: string = page\n      let encodedPathname: string = page\n\n      let fallbackRouteParams: string[] = []\n\n      for (const key of routeParamKeys) {\n        if (fallbackRouteParams.length > 0) {\n          // This is a partial route, so we should add the value to the\n          // fallbackRouteParams.\n          fallbackRouteParams.push(key)\n          continue\n        }\n\n        let paramValue = params[key]\n\n        if (!paramValue) {\n          if (isRoutePPREnabled) {\n            // This is a partial route, so we should add the value to the\n            // fallbackRouteParams.\n            fallbackRouteParams.push(key)\n            continue\n          } else {\n            // This route is not complete, and we aren't performing a partial\n            // prerender, so we should return, skipping this route.\n            return\n          }\n        }\n\n        const { repeat, optional } = regex.groups[key]\n        let replaced = `[${repeat ? '...' : ''}${key}]`\n        if (optional) {\n          replaced = `[${replaced}]`\n        }\n\n        pathname = pathname.replace(\n          replaced,\n          encodeParam(paramValue, (value) => escapePathDelimiters(value, true))\n        )\n        encodedPathname = encodedPathname.replace(\n          replaced,\n          encodeParam(paramValue, encodeURIComponent)\n        )\n      }\n\n      const fallbackRootParams = rootParamKeys.filter((param) =>\n        fallbackRouteParams.includes(param)\n      )\n\n      pathname = normalizePathname(pathname)\n\n      prerenderedRoutesByPathname.set(pathname, {\n        params,\n        pathname,\n        encodedPathname: normalizePathname(encodedPathname),\n        fallbackRouteParams,\n        fallbackMode: dynamicParams\n          ? // If the fallback params includes any root params, then we need to\n            // perform a blocking static render.\n            fallbackRootParams.length > 0\n            ? FallbackMode.BLOCKING_STATIC_RENDER\n            : fallbackMode\n          : FallbackMode.NOT_FOUND,\n        fallbackRootParams,\n        // This is set later after all the routes have been processed.\n        throwOnEmptyStaticShell: true,\n      })\n    })\n  }\n\n  const prerenderedRoutes =\n    prerenderedRoutesByPathname.size > 0 ||\n    lastDynamicSegmentHadGenerateStaticParams\n      ? [...prerenderedRoutesByPathname.values()]\n      : undefined\n\n  // Now we have to set the throwOnEmptyStaticShell for each of the routes.\n  if (prerenderedRoutes && dynamicIO) {\n    assignErrorIfEmpty(prerenderedRoutes, routeParamKeys)\n  }\n\n  return { fallbackMode, prerenderedRoutes }\n}\n"], "names": ["assignErrorIfEmpty", "buildAppStaticPaths", "filterUniqueParams", "filterUniqueRootParamsCombinations", "routeParamKeys", "routeParams", "unique", "Map", "params", "key", "<PERSON><PERSON><PERSON><PERSON>", "value", "valuePart", "Array", "isArray", "join", "undefined", "has", "set", "from", "values", "rootParamKeys", "combinations", "combination", "root<PERSON>ey", "validateParams", "page", "regex", "isRoutePPREnabled", "valid", "length", "some", "Error", "item", "repeat", "optional", "groups", "paramValue", "hasOwnProperty", "push", "prerenderedRoutes", "root", "children", "routes", "route", "currentNode", "valueKey", "childNode", "get", "stack", "node", "pop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "size", "minFallbacks", "Infinity", "r", "Math", "min", "fallbackRouteParams", "throwOnEmptyStaticShell", "child", "dir", "distDir", "dynamicIO", "authInterrupts", "segments", "isrFlushToDisk", "cache<PERSON><PERSON><PERSON>", "cacheLifeProfiles", "requestHeaders", "cacheHandlers", "maxMemoryCacheSize", "fetchCacheKeyPrefix", "nextConfigOutput", "ComponentMod", "buildId", "generate", "config", "dynamicParams", "patchFetch", "incrementalCache", "createIncrementalCache", "flushToDisk", "cacheMaxMemorySize", "getRouteRegex", "Object", "keys", "getRouteMatcher", "after<PERSON><PERSON>ner", "After<PERSON><PERSON>ner", "store", "createWorkStore", "renderOpts", "supportsDynamicResponse", "isRevalidate", "experimental", "waitUntil", "context", "onClose", "onAfterTaskError", "onTaskError", "previouslyRevalidatedTags", "workAsyncStorage", "run", "builtRouteParams", "parents<PERSON><PERSON><PERSON>", "idx", "current", "generateStaticParams", "fetchCache", "parentParams", "result", "executeAfter", "lastDynamicSegmentHadGenerateStaticParams", "segment", "param", "isDynamicSegment", "relative", "filePath", "path", "hadAllParamsGenerated", "every", "supportsRoutePreGeneration", "process", "env", "NODE_ENV", "fallbackMode", "FallbackMode", "PRERENDER", "BLOCKING_STATIC_RENDER", "NOT_FOUND", "prerenderedRoutesByPathname", "unshift", "pathname", "encodedPathname", "fallbackRootParams", "for<PERSON>ach", "replaced", "replace", "encodeParam", "escapePathDelimiters", "encodeURIComponent", "filter", "includes", "normalizePathname"], "mappings": ";;;;;;;;;;;;;;;;;IAkRgBA,kBAAkB;eAAlBA;;IAuIMC,mBAAmB;eAAnBA;;IA7XNC,kBAAkB;eAAlBA;;IA6EAC,kCAAkC;eAAlCA;;;iEApGC;8BACW;2BACI;0BACH;8BACG;4BAIzB;uBAEwC;6EACd;wCACM;;;;;;AAWhC,SAASD,mBACdE,cAAiC,EACjCC,WAA8B;IAE9B,2EAA2E;IAC3E,yEAAyE;IACzE,iCAAiC;IACjC,MAAMC,SAAS,IAAIC;IAEnB,yDAAyD;IACzD,KAAK,MAAMC,UAAUH,YAAa;QAChC,IAAII,MAAM,GAAG,sFAAsF;;QAEnG,yEAAyE;QACzE,0EAA0E;QAC1E,kCAAkC;QAClC,KAAK,MAAMC,YAAYN,eAAgB;YACrC,MAAMO,QAAQH,MAAM,CAACE,SAAS;YAE9B,qEAAqE;YACrE,4FAA4F;YAC5F,qEAAqE;YACrE,0EAA0E;YAC1E,wEAAwE;YACxE,0CAA0C;YAC1C,IAAIE;YACJ,IAAIC,MAAMC,OAAO,CAACH,QAAQ;gBACxBC,YAAY,CAAC,EAAE,EAAED,MAAMI,IAAI,CAAC,MAAM;YACpC,OAAO,IAAIJ,UAAUK,WAAW;gBAC9BJ,YAAY,CAAC,WAAW,CAAC;YAC3B,OAAO;gBACLA,YAAY,CAAC,EAAE,EAAED,OAAO;YAC1B;YACAF,OAAO,GAAGC,SAAS,CAAC,EAAEE,UAAU,CAAC,CAAC;QACpC;QAEA,yEAAyE;QACzE,6DAA6D;QAC7D,IAAI,CAACN,OAAOW,GAAG,CAACR,MAAM;YACpBH,OAAOY,GAAG,CAACT,KAAKD;QAClB;IACF;IAEA,4EAA4E;IAC5E,iBAAiB;IACjB,OAAOK,MAAMM,IAAI,CAACb,OAAOc,MAAM;AACjC;AA+BO,SAASjB,mCACdkB,aAAgC,EAChChB,WAA8B;IAE9B,iEAAiE;IACjE,sEAAsE;IACtE,oEAAoE;IACpE,uBAAuB;IACvB,MAAMiB,eAAe,IAAIf;IAEzB,yDAAyD;IACzD,KAAK,MAAMC,UAAUH,YAAa;QAChC,MAAMkB,cAAsB,CAAC,EAAE,yDAAyD;;QACxF,IAAId,MAAM,GAAG,iGAAiG;;QAE9G,wEAAwE;QACxE,0EAA0E;QAC1E,uCAAuC;QACvC,KAAK,MAAMe,WAAWH,cAAe;YACnC,MAAMV,QAAQH,MAAM,CAACgB,QAAQ;YAC7BD,WAAW,CAACC,QAAQ,GAAGb,MAAM,kEAAkE;;YAE/F,0EAA0E;YAC1E,4FAA4F;YAC5F,yEAAyE;YACzE,iDAAiD;YACjD,IAAIC;YACJ,IAAIC,MAAMC,OAAO,CAACH,QAAQ;gBACxBC,YAAY,CAAC,EAAE,EAAED,MAAMI,IAAI,CAAC,MAAM;YACpC,OAAO,IAAIJ,UAAUK,WAAW;gBAC9BJ,YAAY,CAAC,WAAW,CAAC;YAC3B,OAAO;gBACLA,YAAY,CAAC,EAAE,EAAED,OAAO;YAC1B;YACAF,OAAO,GAAGe,QAAQ,CAAC,EAAEZ,UAAU,CAAC,CAAC;QACnC;QAEA,0EAA0E;QAC1E,uEAAuE;QACvE,IAAI,CAACU,aAAaL,GAAG,CAACR,MAAM;YAC1Ba,aAAaJ,GAAG,CAACT,KAAKc;QACxB;IACF;IAEA,wEAAwE;IACxE,oCAAoC;IACpC,OAAOV,MAAMM,IAAI,CAACG,aAAaF,MAAM;AACvC;AAEA;;;;;;;;;;;CAWC,GACD,SAASK,eACPC,IAAY,EACZC,KAAiB,EACjBC,iBAA0B,EAC1BxB,cAAiC,EACjCiB,aAAgC,EAChChB,WAA8B;IAE9B,MAAMwB,QAAkB,EAAE;IAE1B,4EAA4E;IAC5E,qEAAqE;IACrE,IAAID,qBAAqBP,cAAcS,MAAM,GAAG,GAAG;QACjD,IACEzB,YAAYyB,MAAM,KAAK,KACvBT,cAAcU,IAAI,CAAC,CAACtB,MAClBJ,YAAY0B,IAAI,CAAC,CAACvB,SAAW,CAAEC,CAAAA,OAAOD,MAAK,KAE7C;YACA,IAAIa,cAAcS,MAAM,KAAK,GAAG;gBAC9B,MAAM,qBAEL,CAFK,IAAIE,MACR,CAAC,2BAA2B,EAAEX,aAAa,CAAC,EAAE,CAAC,+CAA+C,EAAEK,KAAK,oCAAoC,CAAC,GADtI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,MAAM,qBAEL,CAFK,IAAIM,MACR,CAAC,sBAAsB,EAAEX,cAAcN,IAAI,CAAC,MAAM,gDAAgD,EAAEW,KAAK,6CAA6C,CAAC,GADnJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,KAAK,MAAMlB,UAAUH,YAAa;QAChC,MAAM4B,OAAe,CAAC;QAEtB,KAAK,MAAMxB,OAAOL,eAAgB;YAChC,MAAM,EAAE8B,MAAM,EAAEC,QAAQ,EAAE,GAAGR,MAAMS,MAAM,CAAC3B,IAAI;YAE9C,IAAI4B,aAAa7B,MAAM,CAACC,IAAI;YAE5B,IACE0B,YACA3B,OAAO8B,cAAc,CAAC7B,QACrB4B,CAAAA,eAAe,QACdA,eAAerB,aACf,AAACqB,eAAuB,KAAI,GAC9B;gBACAA,aAAa,EAAE;YACjB;YAEA,wEAAwE;YACxE,wEAAwE;YACxE,0EAA0E;YAC1E,6CAA6C;YAC7C,IAAI,CAACA,cAAcT,mBAAmB;gBACpC;YACF;YAEA,sEAAsE;YACtE,oBAAoB;YACpB,IAAIM,QAAQ;gBACV,IAAI,CAACrB,MAAMC,OAAO,CAACuB,aAAa;oBAC9B,MAAM,qBAEL,CAFK,IAAIL,MACR,CAAC,sBAAsB,EAAEvB,IAAI,wCAAwC,EAAE,OAAO4B,WAAW,6BAA6B,EAAEX,MAAM,GAD1H,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF,OAAO;gBACL,IAAI,OAAOW,eAAe,UAAU;oBAClC,MAAM,qBAEL,CAFK,IAAIL,MACR,CAAC,sBAAsB,EAAEvB,IAAI,wCAAwC,EAAE,OAAO4B,WAAW,6BAA6B,EAAEX,MAAM,GAD1H,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEAO,IAAI,CAACxB,IAAI,GAAG4B;QACd;QAEAR,MAAMU,IAAI,CAACN;IACb;IAEA,OAAOJ;AACT;AA4BO,SAAS7B,mBACdwC,iBAA8C,EAC9CpC,cAAiC;IAEjC,iDAAiD;IACjD,IAAIoC,kBAAkBV,MAAM,KAAK,GAAG;QAClC;IACF;IAEA,2EAA2E;IAC3E,8CAA8C;IAC9C,MAAMW,OAAiB;QAAEC,UAAU,IAAInC;QAAOoC,QAAQ,EAAE;IAAC;IAEzD,2BAA2B;IAC3B,mEAAmE;IACnE,kEAAkE;IAClE,KAAK,MAAMC,SAASJ,kBAAmB;QACrC,IAAIK,cAAcJ,KAAK,wDAAwD;;QAE/E,0EAA0E;QAC1E,wEAAwE;QACxE,wEAAwE;QACxE,0BAA0B;QAC1B,KAAK,MAAMhC,OAAOL,eAAgB;YAChC,+EAA+E;YAC/E,4EAA4E;YAC5E,6EAA6E;YAC7E,IAAIwC,MAAMpC,MAAM,CAAC8B,cAAc,CAAC7B,MAAM;gBACpC,MAAME,QAAQiC,MAAMpC,MAAM,CAACC,IAAI;gBAE/B,oEAAoE;gBACpE,qEAAqE;gBACrE,kEAAkE;gBAClE,sEAAsE;gBACtE,iEAAiE;gBACjE,qEAAqE;gBACrE,YAAY;gBACZ,IAAIqC;gBACJ,IAAIjC,MAAMC,OAAO,CAACH,QAAQ;oBACxBmC,WAAW,CAAC,EAAE,EAAEnC,MAAMI,IAAI,CAAC,MAAM;gBACnC,OAAO,IAAIJ,UAAUK,WAAW;oBAC9B8B,WAAW,CAAC,WAAW,CAAC;gBAC1B,OAAO;oBACLA,WAAW,CAAC,EAAE,EAAEnC,OAAO;gBACzB;gBAEA,iFAAiF;gBACjF,IAAIoC,YAAYF,YAAYH,QAAQ,CAACM,GAAG,CAACF;gBACzC,IAAI,CAACC,WAAW;oBACd,kEAAkE;oBAClE,+BAA+B;oBAC/BA,YAAY;wBAAEL,UAAU,IAAInC;wBAAOoC,QAAQ,EAAE;oBAAC;oBAC9CE,YAAYH,QAAQ,CAACxB,GAAG,CAAC4B,UAAUC;gBACrC;gBACA,uEAAuE;gBACvEF,cAAcE;YAChB;QACF;QACA,uEAAuE;QACvE,wEAAwE;QACxE,iFAAiF;QACjFF,YAAYF,MAAM,CAACJ,IAAI,CAACK;IAC1B;IAEA,+EAA+E;IAC/E,4EAA4E;IAC5E,+EAA+E;IAC/E,oCAAoC;IACpC,MAAMK,QAAoB;QAACR;KAAK,CAAC,2CAA2C;;IAE5E,MAAOQ,MAAMnB,MAAM,GAAG,EAAG;QACvB,MAAMoB,OAAOD,MAAME,GAAG,EAAI,+CAA+C;;QAEzE,sEAAsE;QACtE,uEAAuE;QACvE,4DAA4D;QAC5D,MAAMC,cAAcF,KAAKR,QAAQ,CAACW,IAAI,GAAG;QAEzC,2EAA2E;QAC3E,6DAA6D;QAC7D,IAAIH,KAAKP,MAAM,CAACb,MAAM,GAAG,GAAG;YAC1B,uEAAuE;YACvE,mEAAmE;YACnE,2GAA2G;YAC3G,8DAA8D;YAC9D,IAAIwB,eAAeC;YACnB,KAAK,MAAMC,KAAKN,KAAKP,MAAM,CAAE;oBAKzBa;gBAJF,+EAA+E;gBAC/E,6DAA6D;gBAC7DF,eAAeG,KAAKC,GAAG,CACrBJ,cACAE,EAAAA,yBAAAA,EAAEG,mBAAmB,qBAArBH,uBAAuB1B,MAAM,KAAI;YAErC;YAEA,8DAA8D;YAC9D,KAAK,MAAMc,SAASM,KAAKP,MAAM,CAAE;gBAC/B,gEAAgE;gBAChE,gEAAgE;gBAChE,+BAA+B;gBAC/B,+EAA+E;gBAC/E,6EAA6E;gBAC7E,0FAA0F;gBAC1F,KAAK;gBACL,uEAAuE;gBACvE,wEAAwE;gBACxE,oFAAoF;gBACpF,0EAA0E;gBAC1E,+EAA+E;gBAC/E,IACES,eACCR,MAAMe,mBAAmB,IACxBf,MAAMe,mBAAmB,CAAC7B,MAAM,GAAGwB,cACrC;oBACAV,MAAMgB,uBAAuB,GAAG,MAAM,0CAA0C;;gBAClF,OAAO;oBACLhB,MAAMgB,uBAAuB,GAAG,KAAK,sCAAsC;;gBAC7E;YACF;QACF;QAEA,uEAAuE;QACvE,+DAA+D;QAC/D,KAAK,MAAMC,SAASX,KAAKR,QAAQ,CAACtB,MAAM,GAAI;YAC1C6B,MAAMV,IAAI,CAACsB;QACb;IACF;AACF;AAQO,eAAe5D,oBAAoB,EACxC6D,GAAG,EACHpC,IAAI,EACJqC,OAAO,EACPC,SAAS,EACTC,cAAc,EACdC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,iBAAiB,EACjBC,cAAc,EACdC,aAAa,EACbC,kBAAkB,EAClBC,mBAAmB,EACnBC,gBAAgB,EAChBC,YAAY,EACZ/C,oBAAoB,KAAK,EACzBgD,OAAO,EACPvD,aAAa,EAsBd;IACC,IACE6C,SAASnC,IAAI,CAAC,CAAC8C;YAAaA;eAAAA,EAAAA,mBAAAA,SAASC,MAAM,qBAAfD,iBAAiBE,aAAa,MAAK;UAC/DL,qBAAqB,UACrB;QACA,MAAM,qBAEL,CAFK,IAAI1C,MACR,mKADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA2C,aAAaK,UAAU;IAEvB,MAAMC,mBAAmB,MAAMC,IAAAA,8CAAsB,EAAC;QACpDpB;QACAC;QACAK;QACAG;QACAD;QACAG;QACAU,aAAahB;QACbiB,oBAAoBZ;IACtB;IAEA,MAAM7C,QAAQ0D,IAAAA,yBAAa,EAAC3D;IAC5B,MAAMtB,iBAAiBkF,OAAOC,IAAI,CAACC,IAAAA,6BAAe,EAAC7D,OAAOD,SAAS,CAAC;IAEpE,MAAM+D,cAAc,IAAIC,yBAAW;IAEnC,MAAMC,QAAQC,IAAAA,0BAAe,EAAC;QAC5BlE;QACA,sEAAsE;QACtE,QAAQ;QACRiC,qBAAqB;QACrBkC,YAAY;YACVZ;YACAZ;YACAyB,yBAAyB;YACzBC,cAAc;YACdC,cAAc;gBACZhC;gBACAC;YACF;YACAgC,WAAWR,YAAYS,OAAO,CAACD,SAAS;YACxCE,SAASV,YAAYS,OAAO,CAACC,OAAO;YACpCC,kBAAkBX,YAAYS,OAAO,CAACG,WAAW;QACnD;QACAzB;QACA0B,2BAA2B,EAAE;IAC/B;IAEA,MAAMjG,cAAc,MAAMsE,aAAa4B,gBAAgB,CAACC,GAAG,CACzDb,OACA;QACE,eAAec,iBACbC,gBAA0B,EAAE,EAC5BC,MAAM,CAAC;YAEP,yDAAyD;YACzD,IAAIA,QAAQzC,SAASpC,MAAM,EAAE,OAAO4E;YAEpC,MAAME,UAAU1C,QAAQ,CAACyC,IAAI;YAE7B,IACE,OAAOC,QAAQC,oBAAoB,KAAK,cACxCF,MAAMzC,SAASpC,MAAM,EACrB;gBACA,OAAO2E,iBAAiBC,eAAeC,MAAM;YAC/C;YAEA,MAAMnG,SAAmB,EAAE;YAE3B,IAAIoG,QAAQC,oBAAoB,EAAE;oBAIrBD;gBAHX,oEAAoE;gBACpE,0EAA0E;gBAC1E,oCAAoC;gBACpC,IAAI,SAAOA,kBAAAA,QAAQ9B,MAAM,qBAAd8B,gBAAgBE,UAAU,MAAK,aAAa;oBACrDnB,MAAMmB,UAAU,GAAGF,QAAQ9B,MAAM,CAACgC,UAAU;gBAC9C;gBAEA,IAAIJ,cAAc5E,MAAM,GAAG,GAAG;oBAC5B,KAAK,MAAMiF,gBAAgBL,cAAe;wBACxC,MAAMM,SAAS,MAAMJ,QAAQC,oBAAoB,CAAC;4BAChDrG,QAAQuG;wBACV;wBAEA,KAAK,MAAM9E,QAAQ+E,OAAQ;4BACzBxG,OAAO+B,IAAI,CAAC;gCAAE,GAAGwE,YAAY;gCAAE,GAAG9E,IAAI;4BAAC;wBACzC;oBACF;gBACF,OAAO;oBACL,MAAM+E,SAAS,MAAMJ,QAAQC,oBAAoB,CAAC;wBAAErG,QAAQ,CAAC;oBAAE;oBAE/DA,OAAO+B,IAAI,IAAIyE;gBACjB;YACF;YAEA,IAAIL,MAAMzC,SAASpC,MAAM,EAAE;gBACzB,OAAO2E,iBAAiBjG,QAAQmG,MAAM;YACxC;YAEA,OAAOnG;QACT;QAEA,OAAOiG;IACT;IAGF,MAAMhB,YAAYwB,YAAY;IAE9B,IAAIC,4CAA4C;IAChD,KAAK,MAAMC,WAAWjD,SAAU;YAM5BiD;QALF,sEAAsE;QACtE,8BAA8B;QAC9B,IACEA,QAAQC,KAAK,IACbD,QAAQE,gBAAgB,IACxBF,EAAAA,kBAAAA,QAAQrC,MAAM,qBAAdqC,gBAAgBpC,aAAa,MAAK,OAClC;YACA,KAAK,MAAMvE,UAAUH,YAAa;gBAChC,IAAI8G,QAAQC,KAAK,IAAI5G,QAAQ;gBAE7B,MAAM8G,WAAWH,QAAQI,QAAQ,GAC7BC,iBAAI,CAACF,QAAQ,CAACxD,KAAKqD,QAAQI,QAAQ,IACnCvG;gBAEJ,MAAM,qBAEL,CAFK,IAAIgB,MACR,CAAC,SAAS,EAAEsF,SAAS,gDAAgD,EAAEH,QAAQC,KAAK,CAAC,6CAA6C,CAAC,GAD/H,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,IACED,QAAQE,gBAAgB,IACxB,OAAOF,QAAQN,oBAAoB,KAAK,YACxC;YACAK,4CAA4C;QAC9C,OAAO,IAAI,OAAOC,QAAQN,oBAAoB,KAAK,YAAY;YAC7DK,4CAA4C;QAC9C;IACF;IAEA,oEAAoE;IACpE,MAAMO,wBACJrH,eAAe0B,MAAM,KAAK,KACzBzB,YAAYyB,MAAM,GAAG,KACpBzB,YAAYqH,KAAK,CAAC,CAAClH;QACjB,KAAK,MAAMC,OAAOL,eAAgB;YAChC,IAAIK,OAAOD,QAAQ;YACnB,OAAO;QACT;QACA,OAAO;IACT;IAEJ,wEAAwE;IACxE,mEAAmE;IACnE,mCAAmC;IACnC,MAAMuE,gBAAgBb,SAASwD,KAAK,CAClC,CAACP;YAAYA;eAAAA,EAAAA,kBAAAA,QAAQrC,MAAM,qBAAdqC,gBAAgBpC,aAAa,MAAK;;IAGjD,MAAM4C,6BACJF,yBAAyBG,QAAQC,GAAG,CAACC,QAAQ,KAAK;IAEpD,MAAMC,eAAehD,gBACjB4C,6BACE/F,oBACEoG,sBAAY,CAACC,SAAS,GACtBD,sBAAY,CAACE,sBAAsB,GACrClH,YACFgH,sBAAY,CAACG,SAAS;IAE1B,MAAMC,8BAA8B,IAAI7H;IAExC,IAAIkH,yBAAyB7F,mBAAmB;QAC9C,IAAIA,mBAAmB;YACrB,wEAAwE;YACxE,uFAAuF;YACvFvB,YAAYgI,OAAO,IACdlI,mCAAmCkB,eAAehB;YAGvD+H,4BAA4BlH,GAAG,CAACQ,MAAM;gBACpClB,QAAQ,CAAC;gBACT8H,UAAU5G;gBACV6G,iBAAiB7G;gBACjBiC,qBAAqBvD;gBACrB2H,cAAchD,gBAEV,oCAAoC;gBACpC1D,cAAcS,MAAM,GAAG,IACrBkG,sBAAY,CAACE,sBAAsB,GACnCH,eACFC,sBAAY,CAACG,SAAS;gBAC1BK,oBAAoBnH;gBACpB,8DAA8D;gBAC9DuC,yBAAyB;YAC3B;QACF;QAEA1D,mBACEE,gBACAqB,eACEC,MACAC,OACAC,mBACAxB,gBACAiB,eACAhB,cAEFoI,OAAO,CAAC,CAACjI;YACT,IAAI8H,WAAmB5G;YACvB,IAAI6G,kBAA0B7G;YAE9B,IAAIiC,sBAAgC,EAAE;YAEtC,KAAK,MAAMlD,OAAOL,eAAgB;gBAChC,IAAIuD,oBAAoB7B,MAAM,GAAG,GAAG;oBAClC,6DAA6D;oBAC7D,uBAAuB;oBACvB6B,oBAAoBpB,IAAI,CAAC9B;oBACzB;gBACF;gBAEA,IAAI4B,aAAa7B,MAAM,CAACC,IAAI;gBAE5B,IAAI,CAAC4B,YAAY;oBACf,IAAIT,mBAAmB;wBACrB,6DAA6D;wBAC7D,uBAAuB;wBACvB+B,oBAAoBpB,IAAI,CAAC9B;wBACzB;oBACF,OAAO;wBACL,iEAAiE;wBACjE,uDAAuD;wBACvD;oBACF;gBACF;gBAEA,MAAM,EAAEyB,MAAM,EAAEC,QAAQ,EAAE,GAAGR,MAAMS,MAAM,CAAC3B,IAAI;gBAC9C,IAAIiI,WAAW,CAAC,CAAC,EAAExG,SAAS,QAAQ,KAAKzB,IAAI,CAAC,CAAC;gBAC/C,IAAI0B,UAAU;oBACZuG,WAAW,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC;gBAC5B;gBAEAJ,WAAWA,SAASK,OAAO,CACzBD,UACAE,IAAAA,kBAAW,EAACvG,YAAY,CAAC1B,QAAUkI,IAAAA,6BAAoB,EAAClI,OAAO;gBAEjE4H,kBAAkBA,gBAAgBI,OAAO,CACvCD,UACAE,IAAAA,kBAAW,EAACvG,YAAYyG;YAE5B;YAEA,MAAMN,qBAAqBnH,cAAc0H,MAAM,CAAC,CAAC3B,QAC/CzD,oBAAoBqF,QAAQ,CAAC5B;YAG/BkB,WAAWW,IAAAA,wBAAiB,EAACX;YAE7BF,4BAA4BlH,GAAG,CAACoH,UAAU;gBACxC9H;gBACA8H;gBACAC,iBAAiBU,IAAAA,wBAAiB,EAACV;gBACnC5E;gBACAoE,cAAchD,gBAEV,oCAAoC;gBACpCyD,mBAAmB1G,MAAM,GAAG,IAC1BkG,sBAAY,CAACE,sBAAsB,GACnCH,eACFC,sBAAY,CAACG,SAAS;gBAC1BK;gBACA,8DAA8D;gBAC9D5E,yBAAyB;YAC3B;QACF;IACF;IAEA,MAAMpB,oBACJ4F,4BAA4B/E,IAAI,GAAG,KACnC6D,4CACI;WAAIkB,4BAA4BhH,MAAM;KAAG,GACzCJ;IAEN,yEAAyE;IACzE,IAAIwB,qBAAqBwB,WAAW;QAClChE,mBAAmBwC,mBAAmBpC;IACxC;IAEA,OAAO;QAAE2H;QAAcvF;IAAkB;AAC3C", "ignoreList": [0]}