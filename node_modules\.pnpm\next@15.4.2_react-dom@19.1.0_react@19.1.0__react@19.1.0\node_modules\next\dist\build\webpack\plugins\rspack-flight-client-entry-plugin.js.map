{"version": 3, "sources": ["../../../../src/build/webpack/plugins/rspack-flight-client-entry-plugin.ts"], "sourcesContent": ["import type { Compiler } from '@rspack/core'\nimport {\n  getInvalidator,\n  getEntries,\n  EntryTypes,\n  getEntryKey,\n} from '../../../server/dev/on-demand-entry-handler'\nimport { COMPILER_NAMES } from '../../../shared/lib/constants'\n\nimport { getProxiedPluginState } from '../../build-context'\nimport { PAGE_TYPES } from '../../../lib/page-types'\nimport { getRspackCore } from '../../../shared/lib/get-rspack'\n\ntype Actions = {\n  [actionId: string]: {\n    workers: {\n      [name: string]: { moduleId: string | number; async: boolean }\n    }\n    // Record which layer the action is in (rsc or sc_action), in the specific entry.\n    layer: {\n      [name: string]: string\n    }\n  }\n}\n\nexport type ActionManifest = {\n  // Assign a unique encryption key during production build.\n  encryptionKey: string\n  node: Actions\n  edge: Actions\n}\n\nexport interface ModuleInfo {\n  moduleId: string | number\n  async: boolean\n}\n\nconst pluginState = getProxiedPluginState({\n  // A map to track \"action\" -> \"list of bundles\".\n  serverActions: {} as ActionManifest['node'],\n  edgeServerActions: {} as ActionManifest['edge'],\n\n  serverActionModules: {} as {\n    [workerName: string]: { server?: ModuleInfo; client?: ModuleInfo }\n  },\n\n  edgeServerActionModules: {} as {\n    [workerName: string]: { server?: ModuleInfo; client?: ModuleInfo }\n  },\n\n  ssrModules: {} as { [ssrModuleId: string]: ModuleInfo },\n  edgeSsrModules: {} as { [ssrModuleId: string]: ModuleInfo },\n\n  rscModules: {} as { [rscModuleId: string]: ModuleInfo },\n  edgeRscModules: {} as { [rscModuleId: string]: ModuleInfo },\n\n  injectedClientEntries: {} as Record<string, string>,\n})\n\ninterface Options {\n  dev: boolean\n  appDir: string\n  isEdgeServer: boolean\n  encryptionKey: string\n}\n\nexport class RspackFlightClientEntryPlugin {\n  plugin: any\n  compiler?: Compiler\n\n  constructor(options: Options) {\n    const { FlightClientEntryPlugin } = getRspackCore()\n\n    this.plugin = new FlightClientEntryPlugin({\n      ...options,\n      builtinAppLoader: !!process.env.BUILTIN_SWC_LOADER,\n      shouldInvalidateCb: ({\n        bundlePath,\n        entryName,\n        absolutePagePath,\n        clientBrowserLoader,\n      }: any) => {\n        let shouldInvalidate = false\n        const compiler = this.compiler!\n\n        const entries = getEntries(compiler.outputPath)\n        const pageKey = getEntryKey(\n          COMPILER_NAMES.client,\n          PAGE_TYPES.APP,\n          bundlePath\n        )\n\n        if (!entries[pageKey]) {\n          entries[pageKey] = {\n            type: EntryTypes.CHILD_ENTRY,\n            parentEntries: new Set([entryName]),\n            absoluteEntryFilePath: absolutePagePath,\n            bundlePath,\n            request: clientBrowserLoader,\n            dispose: false,\n            lastActiveTime: Date.now(),\n          }\n          shouldInvalidate = true\n        } else {\n          const entryData = entries[pageKey]\n          // New version of the client loader\n          if (entryData.request !== clientBrowserLoader) {\n            entryData.request = clientBrowserLoader\n            shouldInvalidate = true\n          }\n          if (entryData.type === EntryTypes.CHILD_ENTRY) {\n            entryData.parentEntries.add(entryName)\n          }\n          entryData.dispose = false\n          entryData.lastActiveTime = Date.now()\n        }\n\n        return shouldInvalidate\n      },\n      invalidateCb: () => {\n        const compiler = this.compiler!\n\n        // Invalidate in development to trigger recompilation\n        const invalidator = getInvalidator(compiler.outputPath)\n        // Check if any of the entry injections need an invalidation\n        if (invalidator) {\n          invalidator.invalidate([COMPILER_NAMES.client])\n        }\n      },\n      stateCb: (state: any) => {\n        Object.assign(pluginState.serverActions, state.serverActions)\n        Object.assign(pluginState.edgeServerActions, state.edgeServerActions)\n        Object.assign(\n          pluginState.serverActionModules,\n          state.serverActionModules\n        )\n        Object.assign(\n          pluginState.edgeServerActionModules,\n          state.edgeServerActionModules\n        )\n        Object.assign(pluginState.ssrModules, state.ssrModules)\n        Object.assign(pluginState.edgeSsrModules, state.edgeSsrModules)\n        Object.assign(pluginState.rscModules, state.rscModules)\n        Object.assign(pluginState.edgeRscModules, state.edgeRscModules)\n        Object.assign(\n          pluginState.injectedClientEntries,\n          state.injectedClientEntries\n        )\n      },\n    })\n  }\n\n  apply(compiler: Compiler) {\n    this.compiler = compiler\n    this.plugin.apply(compiler)\n  }\n}\n"], "names": ["RspackFlightClientEntryPlugin", "pluginState", "getProxiedPluginState", "serverActions", "edgeServerActions", "serverActionModules", "edgeServerActionModules", "ssrModules", "edgeSsrModules", "rscModules", "edgeRscModules", "injectedClientEntries", "constructor", "options", "FlightClientEntryPlugin", "getRspackCore", "plugin", "builtinAppLoader", "process", "env", "BUILTIN_SWC_LOADER", "shouldInvalidateCb", "bundlePath", "entryName", "absolutePagePath", "clientBrowserLoader", "shouldInvalidate", "compiler", "entries", "getEntries", "outputPath", "page<PERSON><PERSON>", "getEntry<PERSON>ey", "COMPILER_NAMES", "client", "PAGE_TYPES", "APP", "type", "EntryTypes", "CHILD_ENTRY", "parentEntries", "Set", "absoluteEntryFilePath", "request", "dispose", "lastActiveTime", "Date", "now", "entryData", "add", "invalidateCb", "invalidator", "getInvalidator", "invalidate", "stateCb", "state", "Object", "assign", "apply"], "mappings": ";;;;+BAkEaA;;;eAAAA;;;sCA5DN;2BACwB;8BAEO;2BACX;2BACG;AA0B9B,MAAMC,cAAcC,IAAAA,mCAAqB,EAAC;IACxC,gDAAgD;IAChDC,eAAe,CAAC;IAChBC,mBAAmB,CAAC;IAEpBC,qBAAqB,CAAC;IAItBC,yBAAyB,CAAC;IAI1BC,YAAY,CAAC;IACbC,gBAAgB,CAAC;IAEjBC,YAAY,CAAC;IACbC,gBAAgB,CAAC;IAEjBC,uBAAuB,CAAC;AAC1B;AASO,MAAMX;IAIXY,YAAYC,OAAgB,CAAE;QAC5B,MAAM,EAAEC,uBAAuB,EAAE,GAAGC,IAAAA,wBAAa;QAEjD,IAAI,CAACC,MAAM,GAAG,IAAIF,wBAAwB;YACxC,GAAGD,OAAO;YACVI,kBAAkB,CAAC,CAACC,QAAQC,GAAG,CAACC,kBAAkB;YAClDC,oBAAoB,CAAC,EACnBC,UAAU,EACVC,SAAS,EACTC,gBAAgB,EAChBC,mBAAmB,EACf;gBACJ,IAAIC,mBAAmB;gBACvB,MAAMC,WAAW,IAAI,CAACA,QAAQ;gBAE9B,MAAMC,UAAUC,IAAAA,gCAAU,EAACF,SAASG,UAAU;gBAC9C,MAAMC,UAAUC,IAAAA,iCAAW,EACzBC,yBAAc,CAACC,MAAM,EACrBC,qBAAU,CAACC,GAAG,EACdd;gBAGF,IAAI,CAACM,OAAO,CAACG,QAAQ,EAAE;oBACrBH,OAAO,CAACG,QAAQ,GAAG;wBACjBM,MAAMC,gCAAU,CAACC,WAAW;wBAC5BC,eAAe,IAAIC,IAAI;4BAAClB;yBAAU;wBAClCmB,uBAAuBlB;wBACvBF;wBACAqB,SAASlB;wBACTmB,SAAS;wBACTC,gBAAgBC,KAAKC,GAAG;oBAC1B;oBACArB,mBAAmB;gBACrB,OAAO;oBACL,MAAMsB,YAAYpB,OAAO,CAACG,QAAQ;oBAClC,mCAAmC;oBACnC,IAAIiB,UAAUL,OAAO,KAAKlB,qBAAqB;wBAC7CuB,UAAUL,OAAO,GAAGlB;wBACpBC,mBAAmB;oBACrB;oBACA,IAAIsB,UAAUX,IAAI,KAAKC,gCAAU,CAACC,WAAW,EAAE;wBAC7CS,UAAUR,aAAa,CAACS,GAAG,CAAC1B;oBAC9B;oBACAyB,UAAUJ,OAAO,GAAG;oBACpBI,UAAUH,cAAc,GAAGC,KAAKC,GAAG;gBACrC;gBAEA,OAAOrB;YACT;YACAwB,cAAc;gBACZ,MAAMvB,WAAW,IAAI,CAACA,QAAQ;gBAE9B,qDAAqD;gBACrD,MAAMwB,cAAcC,IAAAA,oCAAc,EAACzB,SAASG,UAAU;gBACtD,4DAA4D;gBAC5D,IAAIqB,aAAa;oBACfA,YAAYE,UAAU,CAAC;wBAACpB,yBAAc,CAACC,MAAM;qBAAC;gBAChD;YACF;YACAoB,SAAS,CAACC;gBACRC,OAAOC,MAAM,CAACxD,YAAYE,aAAa,EAAEoD,MAAMpD,aAAa;gBAC5DqD,OAAOC,MAAM,CAACxD,YAAYG,iBAAiB,EAAEmD,MAAMnD,iBAAiB;gBACpEoD,OAAOC,MAAM,CACXxD,YAAYI,mBAAmB,EAC/BkD,MAAMlD,mBAAmB;gBAE3BmD,OAAOC,MAAM,CACXxD,YAAYK,uBAAuB,EACnCiD,MAAMjD,uBAAuB;gBAE/BkD,OAAOC,MAAM,CAACxD,YAAYM,UAAU,EAAEgD,MAAMhD,UAAU;gBACtDiD,OAAOC,MAAM,CAACxD,YAAYO,cAAc,EAAE+C,MAAM/C,cAAc;gBAC9DgD,OAAOC,MAAM,CAACxD,YAAYQ,UAAU,EAAE8C,MAAM9C,UAAU;gBACtD+C,OAAOC,MAAM,CAACxD,YAAYS,cAAc,EAAE6C,MAAM7C,cAAc;gBAC9D8C,OAAOC,MAAM,CACXxD,YAAYU,qBAAqB,EACjC4C,MAAM5C,qBAAqB;YAE/B;QACF;IACF;IAEA+C,MAAM/B,QAAkB,EAAE;QACxB,IAAI,CAACA,QAAQ,GAAGA;QAChB,IAAI,CAACX,MAAM,CAAC0C,KAAK,CAAC/B;IACpB;AACF", "ignoreList": [0]}