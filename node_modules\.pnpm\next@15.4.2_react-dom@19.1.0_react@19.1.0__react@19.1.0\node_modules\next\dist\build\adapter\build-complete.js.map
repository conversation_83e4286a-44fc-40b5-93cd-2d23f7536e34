{"version": 3, "sources": ["../../../src/build/adapter/build-complete.ts"], "sourcesContent": ["import path from 'path'\nimport fs from 'fs/promises'\nimport { promisify } from 'util'\nimport { pathToFileURL } from 'url'\nimport * as Log from '../output/log'\nimport globOriginal from 'next/dist/compiled/glob'\nimport { interopDefault } from '../../lib/interop-default'\nimport type { AdapterOutputs, NextAdapter } from '../../server/config-shared'\nimport {\n  RouteType,\n  type FunctionsConfigManifest,\n  type PrerenderManifest,\n  type RoutesManifest,\n} from '..'\nimport type {\n  EdgeFunctionDefinition,\n  MiddlewareManifest,\n} from '../webpack/plugins/middleware-plugin'\nimport { isMiddlewareFilename } from '../utils'\nimport { normalizePagePath } from '../../shared/lib/page-path/normalize-page-path'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\n\nconst glob = promisify(globOriginal)\n\nexport async function handleBuildComplete({\n  // dir,\n  distDir,\n  tracingRoot,\n  adapterPath,\n  pageKeys,\n  appPageKeys,\n  hasNodeMiddleware,\n  hasInstrumentationHook,\n  requiredServerFiles,\n  routesManifest,\n  // prerenderManifest,\n  middlewareManifest,\n}: {\n  dir: string\n  distDir: string\n  adapterPath: string\n  tracingRoot: string\n  hasNodeMiddleware: boolean\n  pageKeys: readonly string[]\n  hasInstrumentationHook: boolean\n  appPageKeys?: readonly string[] | undefined\n  requiredServerFiles: string[]\n  routesManifest: RoutesManifest\n  prerenderManifest: PrerenderManifest\n  middlewareManifest: MiddlewareManifest\n  functionsConfigManifest: FunctionsConfigManifest\n}) {\n  const adapterMod = interopDefault(\n    await import(pathToFileURL(require.resolve(adapterPath)).href)\n  ) as NextAdapter\n\n  if (typeof adapterMod.onBuildComplete === 'function') {\n    Log.info(`Running onBuildComplete from ${adapterMod.name}`)\n\n    try {\n      const outputs: AdapterOutputs = []\n\n      const staticFiles = await glob('**/*', {\n        cwd: path.join(distDir, 'static'),\n      })\n\n      for (const file of staticFiles) {\n        const pathname = path.posix.join('/_next/static', file)\n        const filePath = path.join(distDir, 'static', file)\n        outputs.push({\n          type: RouteType.STATIC_FILE,\n          id: path.join('static', file),\n          pathname,\n          filePath,\n        })\n      }\n\n      const sharedNodeAssets: Record<string, string> = {}\n\n      for (const file of requiredServerFiles) {\n        // add to shared node assets\n        const filePath = path.join(distDir, file)\n        const fileOutputPath = path.relative(tracingRoot, filePath)\n        sharedNodeAssets[fileOutputPath] = filePath\n      }\n\n      if (hasInstrumentationHook) {\n        const assets = await handleTraceFiles(\n          path.join(distDir, 'server', 'instrumentation.js.nft.json')\n        )\n        const fileOutputPath = path.relative(\n          tracingRoot,\n          path.join(distDir, 'server', 'instrumentation.js')\n        )\n        sharedNodeAssets[fileOutputPath] = path.join(\n          distDir,\n          'server',\n          'instrumentation.js'\n        )\n        Object.assign(sharedNodeAssets, assets)\n      }\n\n      async function handleTraceFiles(\n        traceFilePath: string\n      ): Promise<Record<string, string>> {\n        const assets: Record<string, string> = Object.assign(\n          {},\n          sharedNodeAssets\n        )\n        const traceData = JSON.parse(\n          await fs.readFile(traceFilePath, 'utf8')\n        ) as {\n          files: string[]\n        }\n        const traceFileDir = path.dirname(traceFilePath)\n\n        for (const relativeFile of traceData.files) {\n          const tracedFilePath = path.join(traceFileDir, relativeFile)\n          const fileOutputPath = path.relative(tracingRoot, tracedFilePath)\n          assets[fileOutputPath] = tracedFilePath\n        }\n        return assets\n      }\n\n      async function handleEdgeFunction(\n        page: EdgeFunctionDefinition,\n        isMiddleware: boolean = false\n      ) {\n        let type = RouteType.PAGES\n        const isAppPrefix = page.page.startsWith('app/')\n        const isAppPage = isAppPrefix && page.page.endsWith('/page')\n        const isAppRoute = isAppPrefix && page.page.endsWith('/route')\n\n        if (isMiddleware) {\n          type = RouteType.MIDDLEWARE\n        } else if (isAppPage) {\n          type = RouteType.APP_PAGE\n        } else if (isAppRoute) {\n          type = RouteType.APP_ROUTE\n        } else if (page.page.startsWith('/api')) {\n          type = RouteType.PAGES_API\n        }\n\n        const output: AdapterOutputs[0] = {\n          id: page.name,\n          runtime: 'edge',\n          pathname: isAppPrefix ? normalizeAppPath(page.name) : page.name,\n          filePath: path.join(\n            distDir,\n            'server',\n            page.files.find(\n              (item) =>\n                item.startsWith('server/app') || item.startsWith('server/pages')\n            ) || ''\n          ),\n          assets: {},\n          type,\n        }\n\n        function handleFile(file: string) {\n          const originalPath = path.join(distDir, file)\n          const fileOutputPath = path.join(\n            path.relative(tracingRoot, distDir),\n            file\n          )\n          if (!output.assets) {\n            output.assets = {}\n          }\n          output.assets[fileOutputPath] = originalPath\n        }\n        for (const file of page.files) {\n          handleFile(file)\n        }\n        for (const item of [...(page.wasm || []), ...(page.assets || [])]) {\n          handleFile(item.filePath)\n        }\n        outputs.push(output)\n      }\n\n      const edgeFunctionHandlers: Promise<any>[] = []\n\n      for (const middleware of Object.values(middlewareManifest.middleware)) {\n        if (isMiddlewareFilename(middleware.name)) {\n          edgeFunctionHandlers.push(handleEdgeFunction(middleware, true))\n        }\n      }\n\n      for (const page of Object.values(middlewareManifest.functions)) {\n        edgeFunctionHandlers.push(handleEdgeFunction(page))\n      }\n\n      for (const page of pageKeys) {\n        if (middlewareManifest.functions.hasOwnProperty(page)) {\n          continue\n        }\n        const route = normalizePagePath(page)\n\n        const pageFile = path.join(\n          distDir,\n          'server',\n          'pages',\n          `${normalizePagePath(page)}.js`\n        )\n        const pageTraceFile = `${pageFile}.nft.json`\n        const assets = await handleTraceFiles(pageTraceFile).catch((err) => {\n          if (err.code !== 'ENOENT' || (page !== '/404' && page !== '/500')) {\n            Log.warn(`Failed to copy traced files for ${pageFile}`, err)\n          }\n          return {} as Record<string, string>\n        })\n\n        outputs.push({\n          id: route,\n          type: page.startsWith('/api') ? RouteType.PAGES_API : RouteType.PAGES,\n          filePath: pageTraceFile.replace(/\\.nft\\.json$/, ''),\n          pathname: route,\n          assets,\n          runtime: 'nodejs',\n        })\n      }\n\n      if (hasNodeMiddleware) {\n        const middlewareFile = path.join(distDir, 'server', 'middleware.js')\n        const middlewareTrace = `${middlewareFile}.nft.json`\n        const assets = await handleTraceFiles(middlewareTrace)\n\n        outputs.push({\n          pathname: '/_middleware',\n          id: '/_middleware',\n          assets,\n          type: RouteType.MIDDLEWARE,\n          runtime: 'nodejs',\n          filePath: middlewareFile,\n        })\n      }\n\n      if (appPageKeys) {\n        for (const page of appPageKeys) {\n          if (middlewareManifest.functions.hasOwnProperty(page)) {\n            continue\n          }\n          const normalizedPage = normalizeAppPath(page)\n          const pageFile = path.join(distDir, 'server', 'app', `${page}.js`)\n          const pageTraceFile = `${pageFile}.nft.json`\n          const assets = await handleTraceFiles(pageTraceFile).catch((err) => {\n            Log.warn(`Failed to copy traced files for ${pageFile}`, err)\n            return {} as Record<string, string>\n          })\n\n          outputs.push({\n            pathname: normalizedPage,\n            id: normalizedPage,\n            assets,\n            type: page.endsWith('/route')\n              ? RouteType.APP_ROUTE\n              : RouteType.APP_PAGE,\n            runtime: 'nodejs',\n            filePath: pageFile,\n          })\n        }\n      }\n\n      // TODO: prerender assets\n\n      await adapterMod.onBuildComplete({\n        routes: {\n          dynamicRoutes: routesManifest.dynamicRoutes,\n          rewrites: routesManifest.rewrites,\n          redirects: routesManifest.redirects,\n          headers: routesManifest.headers,\n        },\n        outputs,\n      })\n    } catch (err) {\n      Log.error(`Failed to run onBuildComplete from ${adapterMod.name}`)\n      throw err\n    }\n  }\n}\n"], "names": ["handleBuildComplete", "glob", "promisify", "globOriginal", "distDir", "tracingRoot", "adapterPath", "pageKeys", "appPageKeys", "hasNodeMiddleware", "hasInstrumentationHook", "requiredServerFiles", "routesManifest", "middlewareManifest", "adapterMod", "interopDefault", "pathToFileURL", "require", "resolve", "href", "onBuildComplete", "Log", "info", "name", "outputs", "staticFiles", "cwd", "path", "join", "file", "pathname", "posix", "filePath", "push", "type", "RouteType", "STATIC_FILE", "id", "sharedNodeAssets", "fileOutputPath", "relative", "assets", "handleTraceFiles", "Object", "assign", "traceFilePath", "traceData", "JSON", "parse", "fs", "readFile", "traceFileDir", "dirname", "relativeFile", "files", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleEdgeFunction", "page", "isMiddleware", "PAGES", "isAppPrefix", "startsWith", "isAppPage", "endsWith", "isAppRoute", "MIDDLEWARE", "APP_PAGE", "APP_ROUTE", "PAGES_API", "output", "runtime", "normalizeAppPath", "find", "item", "handleFile", "originalPath", "wasm", "edgeFunctionHandlers", "middleware", "values", "isMiddlewareFilename", "functions", "hasOwnProperty", "route", "normalizePagePath", "pageFile", "pageTraceFile", "catch", "err", "code", "warn", "replace", "middlewareFile", "middlewareTrace", "normalizedPage", "routes", "dynamicRoutes", "rewrites", "redirects", "headers", "error"], "mappings": ";;;;+BAwBsBA;;;eAAAA;;;6DAxBL;iEACF;sBACW;qBACI;6DACT;6DACI;gCACM;kBAOxB;uBAK8B;mCACH;0BACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjC,MAAMC,OAAOC,IAAAA,eAAS,EAACC,aAAY;AAE5B,eAAeH,oBAAoB,EACxC,OAAO;AACPI,OAAO,EACPC,WAAW,EACXC,WAAW,EACXC,QAAQ,EACRC,WAAW,EACXC,iBAAiB,EACjBC,sBAAsB,EACtBC,mBAAmB,EACnBC,cAAc,EACd,qBAAqB;AACrBC,kBAAkB,EAenB;IACC,MAAMC,aAAaC,IAAAA,8BAAc,EAC/B,MAAM,MAAM,CAACC,IAAAA,kBAAa,EAACC,QAAQC,OAAO,CAACZ,cAAca,IAAI;IAG/D,IAAI,OAAOL,WAAWM,eAAe,KAAK,YAAY;QACpDC,KAAIC,IAAI,CAAC,CAAC,6BAA6B,EAAER,WAAWS,IAAI,EAAE;QAE1D,IAAI;YACF,MAAMC,UAA0B,EAAE;YAElC,MAAMC,cAAc,MAAMxB,KAAK,QAAQ;gBACrCyB,KAAKC,aAAI,CAACC,IAAI,CAACxB,SAAS;YAC1B;YAEA,KAAK,MAAMyB,QAAQJ,YAAa;gBAC9B,MAAMK,WAAWH,aAAI,CAACI,KAAK,CAACH,IAAI,CAAC,iBAAiBC;gBAClD,MAAMG,WAAWL,aAAI,CAACC,IAAI,CAACxB,SAAS,UAAUyB;gBAC9CL,QAAQS,IAAI,CAAC;oBACXC,MAAMC,WAAS,CAACC,WAAW;oBAC3BC,IAAIV,aAAI,CAACC,IAAI,CAAC,UAAUC;oBACxBC;oBACAE;gBACF;YACF;YAEA,MAAMM,mBAA2C,CAAC;YAElD,KAAK,MAAMT,QAAQlB,oBAAqB;gBACtC,4BAA4B;gBAC5B,MAAMqB,WAAWL,aAAI,CAACC,IAAI,CAACxB,SAASyB;gBACpC,MAAMU,iBAAiBZ,aAAI,CAACa,QAAQ,CAACnC,aAAa2B;gBAClDM,gBAAgB,CAACC,eAAe,GAAGP;YACrC;YAEA,IAAItB,wBAAwB;gBAC1B,MAAM+B,SAAS,MAAMC,iBACnBf,aAAI,CAACC,IAAI,CAACxB,SAAS,UAAU;gBAE/B,MAAMmC,iBAAiBZ,aAAI,CAACa,QAAQ,CAClCnC,aACAsB,aAAI,CAACC,IAAI,CAACxB,SAAS,UAAU;gBAE/BkC,gBAAgB,CAACC,eAAe,GAAGZ,aAAI,CAACC,IAAI,CAC1CxB,SACA,UACA;gBAEFuC,OAAOC,MAAM,CAACN,kBAAkBG;YAClC;YAEA,eAAeC,iBACbG,aAAqB;gBAErB,MAAMJ,SAAiCE,OAAOC,MAAM,CAClD,CAAC,GACDN;gBAEF,MAAMQ,YAAYC,KAAKC,KAAK,CAC1B,MAAMC,iBAAE,CAACC,QAAQ,CAACL,eAAe;gBAInC,MAAMM,eAAexB,aAAI,CAACyB,OAAO,CAACP;gBAElC,KAAK,MAAMQ,gBAAgBP,UAAUQ,KAAK,CAAE;oBAC1C,MAAMC,iBAAiB5B,aAAI,CAACC,IAAI,CAACuB,cAAcE;oBAC/C,MAAMd,iBAAiBZ,aAAI,CAACa,QAAQ,CAACnC,aAAakD;oBAClDd,MAAM,CAACF,eAAe,GAAGgB;gBAC3B;gBACA,OAAOd;YACT;YAEA,eAAee,mBACbC,IAA4B,EAC5BC,eAAwB,KAAK;gBAE7B,IAAIxB,OAAOC,WAAS,CAACwB,KAAK;gBAC1B,MAAMC,cAAcH,KAAKA,IAAI,CAACI,UAAU,CAAC;gBACzC,MAAMC,YAAYF,eAAeH,KAAKA,IAAI,CAACM,QAAQ,CAAC;gBACpD,MAAMC,aAAaJ,eAAeH,KAAKA,IAAI,CAACM,QAAQ,CAAC;gBAErD,IAAIL,cAAc;oBAChBxB,OAAOC,WAAS,CAAC8B,UAAU;gBAC7B,OAAO,IAAIH,WAAW;oBACpB5B,OAAOC,WAAS,CAAC+B,QAAQ;gBAC3B,OAAO,IAAIF,YAAY;oBACrB9B,OAAOC,WAAS,CAACgC,SAAS;gBAC5B,OAAO,IAAIV,KAAKA,IAAI,CAACI,UAAU,CAAC,SAAS;oBACvC3B,OAAOC,WAAS,CAACiC,SAAS;gBAC5B;gBAEA,MAAMC,SAA4B;oBAChChC,IAAIoB,KAAKlC,IAAI;oBACb+C,SAAS;oBACTxC,UAAU8B,cAAcW,IAAAA,0BAAgB,EAACd,KAAKlC,IAAI,IAAIkC,KAAKlC,IAAI;oBAC/DS,UAAUL,aAAI,CAACC,IAAI,CACjBxB,SACA,UACAqD,KAAKH,KAAK,CAACkB,IAAI,CACb,CAACC,OACCA,KAAKZ,UAAU,CAAC,iBAAiBY,KAAKZ,UAAU,CAAC,oBAChD;oBAEPpB,QAAQ,CAAC;oBACTP;gBACF;gBAEA,SAASwC,WAAW7C,IAAY;oBAC9B,MAAM8C,eAAehD,aAAI,CAACC,IAAI,CAACxB,SAASyB;oBACxC,MAAMU,iBAAiBZ,aAAI,CAACC,IAAI,CAC9BD,aAAI,CAACa,QAAQ,CAACnC,aAAaD,UAC3ByB;oBAEF,IAAI,CAACwC,OAAO5B,MAAM,EAAE;wBAClB4B,OAAO5B,MAAM,GAAG,CAAC;oBACnB;oBACA4B,OAAO5B,MAAM,CAACF,eAAe,GAAGoC;gBAClC;gBACA,KAAK,MAAM9C,QAAQ4B,KAAKH,KAAK,CAAE;oBAC7BoB,WAAW7C;gBACb;gBACA,KAAK,MAAM4C,QAAQ;uBAAKhB,KAAKmB,IAAI,IAAI,EAAE;uBAAOnB,KAAKhB,MAAM,IAAI,EAAE;iBAAE,CAAE;oBACjEiC,WAAWD,KAAKzC,QAAQ;gBAC1B;gBACAR,QAAQS,IAAI,CAACoC;YACf;YAEA,MAAMQ,uBAAuC,EAAE;YAE/C,KAAK,MAAMC,cAAcnC,OAAOoC,MAAM,CAAClE,mBAAmBiE,UAAU,EAAG;gBACrE,IAAIE,IAAAA,2BAAoB,EAACF,WAAWvD,IAAI,GAAG;oBACzCsD,qBAAqB5C,IAAI,CAACuB,mBAAmBsB,YAAY;gBAC3D;YACF;YAEA,KAAK,MAAMrB,QAAQd,OAAOoC,MAAM,CAAClE,mBAAmBoE,SAAS,EAAG;gBAC9DJ,qBAAqB5C,IAAI,CAACuB,mBAAmBC;YAC/C;YAEA,KAAK,MAAMA,QAAQlD,SAAU;gBAC3B,IAAIM,mBAAmBoE,SAAS,CAACC,cAAc,CAACzB,OAAO;oBACrD;gBACF;gBACA,MAAM0B,QAAQC,IAAAA,oCAAiB,EAAC3B;gBAEhC,MAAM4B,WAAW1D,aAAI,CAACC,IAAI,CACxBxB,SACA,UACA,SACA,GAAGgF,IAAAA,oCAAiB,EAAC3B,MAAM,GAAG,CAAC;gBAEjC,MAAM6B,gBAAgB,GAAGD,SAAS,SAAS,CAAC;gBAC5C,MAAM5C,SAAS,MAAMC,iBAAiB4C,eAAeC,KAAK,CAAC,CAACC;oBAC1D,IAAIA,IAAIC,IAAI,KAAK,YAAahC,SAAS,UAAUA,SAAS,QAAS;wBACjEpC,KAAIqE,IAAI,CAAC,CAAC,gCAAgC,EAAEL,UAAU,EAAEG;oBAC1D;oBACA,OAAO,CAAC;gBACV;gBAEAhE,QAAQS,IAAI,CAAC;oBACXI,IAAI8C;oBACJjD,MAAMuB,KAAKI,UAAU,CAAC,UAAU1B,WAAS,CAACiC,SAAS,GAAGjC,WAAS,CAACwB,KAAK;oBACrE3B,UAAUsD,cAAcK,OAAO,CAAC,gBAAgB;oBAChD7D,UAAUqD;oBACV1C;oBACA6B,SAAS;gBACX;YACF;YAEA,IAAI7D,mBAAmB;gBACrB,MAAMmF,iBAAiBjE,aAAI,CAACC,IAAI,CAACxB,SAAS,UAAU;gBACpD,MAAMyF,kBAAkB,GAAGD,eAAe,SAAS,CAAC;gBACpD,MAAMnD,SAAS,MAAMC,iBAAiBmD;gBAEtCrE,QAAQS,IAAI,CAAC;oBACXH,UAAU;oBACVO,IAAI;oBACJI;oBACAP,MAAMC,WAAS,CAAC8B,UAAU;oBAC1BK,SAAS;oBACTtC,UAAU4D;gBACZ;YACF;YAEA,IAAIpF,aAAa;gBACf,KAAK,MAAMiD,QAAQjD,YAAa;oBAC9B,IAAIK,mBAAmBoE,SAAS,CAACC,cAAc,CAACzB,OAAO;wBACrD;oBACF;oBACA,MAAMqC,iBAAiBvB,IAAAA,0BAAgB,EAACd;oBACxC,MAAM4B,WAAW1D,aAAI,CAACC,IAAI,CAACxB,SAAS,UAAU,OAAO,GAAGqD,KAAK,GAAG,CAAC;oBACjE,MAAM6B,gBAAgB,GAAGD,SAAS,SAAS,CAAC;oBAC5C,MAAM5C,SAAS,MAAMC,iBAAiB4C,eAAeC,KAAK,CAAC,CAACC;wBAC1DnE,KAAIqE,IAAI,CAAC,CAAC,gCAAgC,EAAEL,UAAU,EAAEG;wBACxD,OAAO,CAAC;oBACV;oBAEAhE,QAAQS,IAAI,CAAC;wBACXH,UAAUgE;wBACVzD,IAAIyD;wBACJrD;wBACAP,MAAMuB,KAAKM,QAAQ,CAAC,YAChB5B,WAAS,CAACgC,SAAS,GACnBhC,WAAS,CAAC+B,QAAQ;wBACtBI,SAAS;wBACTtC,UAAUqD;oBACZ;gBACF;YACF;YAEA,yBAAyB;YAEzB,MAAMvE,WAAWM,eAAe,CAAC;gBAC/B2E,QAAQ;oBACNC,eAAepF,eAAeoF,aAAa;oBAC3CC,UAAUrF,eAAeqF,QAAQ;oBACjCC,WAAWtF,eAAesF,SAAS;oBACnCC,SAASvF,eAAeuF,OAAO;gBACjC;gBACA3E;YACF;QACF,EAAE,OAAOgE,KAAK;YACZnE,KAAI+E,KAAK,CAAC,CAAC,mCAAmC,EAAEtF,WAAWS,IAAI,EAAE;YACjE,MAAMiE;QACR;IACF;AACF", "ignoreList": [0]}