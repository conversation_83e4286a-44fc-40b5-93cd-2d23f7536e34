{"version": 3, "sources": ["../../../src/build/next-config-ts/transpile-config.ts"], "sourcesContent": ["import type { Options as SWCOptions } from '@swc/core'\nimport type { CompilerOptions } from 'typescript'\n\nimport { resolve } from 'node:path'\nimport { readFile } from 'node:fs/promises'\nimport { deregisterHook, registerHook, requireFromString } from './require-hook'\nimport { warn } from '../output/log'\nimport { installDependencies } from '../../lib/install-dependencies'\n\nfunction resolveSWCOptions(\n  cwd: string,\n  compilerOptions: CompilerOptions\n): SWCOptions {\n  const resolvedBaseUrl = resolve(cwd, compilerOptions.baseUrl ?? '.')\n  return {\n    jsc: {\n      target: 'es5',\n      parser: {\n        syntax: 'typescript',\n      },\n      paths: compilerOptions.paths,\n      baseUrl: resolvedBaseUrl,\n    },\n    module: {\n      type: 'commonjs',\n    },\n    isModule: 'unknown',\n  } satisfies SWCOptions\n}\n\n// Ported from next/src/lib/verify-typescript-setup.ts\n// Although this overlaps with the later `verifyTypeScriptSetup`,\n// it is acceptable since the time difference in the worst case is trivial,\n// as we are only preparing to install the dependencies once more.\nasync function verifyTypeScriptSetup(cwd: string, configFileName: string) {\n  try {\n    // Quick module check.\n    require.resolve('typescript', { paths: [cwd] })\n  } catch (error) {\n    if (\n      error &&\n      typeof error === 'object' &&\n      'code' in error &&\n      error.code === 'MODULE_NOT_FOUND'\n    ) {\n      warn(\n        `Installing TypeScript as it was not found while loading \"${configFileName}\".`\n      )\n\n      await installDependencies(cwd, [{ pkg: 'typescript' }], true).catch(\n        (err) => {\n          if (err && typeof err === 'object' && 'command' in err) {\n            console.error(\n              `Failed to install TypeScript, please install it manually to continue:\\n` +\n                (err as any).command +\n                '\\n'\n            )\n          }\n          throw err\n        }\n      )\n    }\n  }\n}\n\nasync function getTsConfig(cwd: string): Promise<CompilerOptions> {\n  const ts: typeof import('typescript') = require(\n    require.resolve('typescript', { paths: [cwd] })\n  )\n\n  // NOTE: This doesn't fully cover the edge case for setting\n  // \"typescript.tsconfigPath\" in next config which is currently\n  // a restriction.\n  const tsConfigPath = ts.findConfigFile(\n    cwd,\n    ts.sys.fileExists,\n    'tsconfig.json'\n  )\n\n  if (!tsConfigPath) {\n    // It is ok to not return ts.getDefaultCompilerOptions() because\n    // we are only lookfing for paths and baseUrl from tsConfig.\n    return {}\n  }\n\n  const configFile = ts.readConfigFile(tsConfigPath, ts.sys.readFile)\n  const parsedCommandLine = ts.parseJsonConfigFileContent(\n    configFile.config,\n    ts.sys,\n    cwd\n  )\n\n  return parsedCommandLine.options\n}\n\nexport async function transpileConfig({\n  nextConfigPath,\n  configFileName,\n  cwd,\n}: {\n  nextConfigPath: string\n  configFileName: string\n  cwd: string\n}) {\n  let hasRequire = false\n  try {\n    // Ensure TypeScript is installed to use the API.\n    await verifyTypeScriptSetup(cwd, configFileName)\n\n    const compilerOptions = await getTsConfig(cwd)\n    const swcOptions = resolveSWCOptions(cwd, compilerOptions)\n\n    const nextConfigString = await readFile(nextConfigPath, 'utf8')\n    // lazy require swc since it loads React before even setting NODE_ENV\n    // resulting loading Development React on Production\n    const { transform } = require('../swc') as typeof import('../swc')\n    const { code } = await transform(nextConfigString, swcOptions)\n\n    // register require hook only if require exists\n    if (code.includes('require(')) {\n      registerHook(swcOptions)\n      hasRequire = true\n    }\n\n    // filename & extension don't matter here\n    return requireFromString(code, resolve(cwd, 'next.config.compiled.js'))\n  } catch (error) {\n    throw error\n  } finally {\n    if (hasRequire) {\n      deregisterHook()\n    }\n  }\n}\n"], "names": ["transpileConfig", "resolveSWCOptions", "cwd", "compilerOptions", "resolvedBaseUrl", "resolve", "baseUrl", "jsc", "target", "parser", "syntax", "paths", "module", "type", "isModule", "verifyTypeScriptSetup", "configFileName", "require", "error", "code", "warn", "installDependencies", "pkg", "catch", "err", "console", "command", "getTsConfig", "ts", "tsConfigPath", "findConfigFile", "sys", "fileExists", "configFile", "readConfigFile", "readFile", "parsedCommandLine", "parseJsonConfigFileContent", "config", "options", "nextConfigPath", "hasRequire", "swcOptions", "nextConfigString", "transform", "includes", "registerHook", "requireFromString", "deregisterHook"], "mappings": ";;;;+BA+FsBA;;;eAAAA;;;0BA5FE;0BACC;6BACuC;qBAC3C;qCACe;AAEpC,SAASC,kBACPC,GAAW,EACXC,eAAgC;IAEhC,MAAMC,kBAAkBC,IAAAA,iBAAO,EAACH,KAAKC,gBAAgBG,OAAO,IAAI;IAChE,OAAO;QACLC,KAAK;YACHC,QAAQ;YACRC,QAAQ;gBACNC,QAAQ;YACV;YACAC,OAAOR,gBAAgBQ,KAAK;YAC5BL,SAASF;QACX;QACAQ,QAAQ;YACNC,MAAM;QACR;QACAC,UAAU;IACZ;AACF;AAEA,sDAAsD;AACtD,iEAAiE;AACjE,2EAA2E;AAC3E,kEAAkE;AAClE,eAAeC,sBAAsBb,GAAW,EAAEc,cAAsB;IACtE,IAAI;QACF,sBAAsB;QACtBC,QAAQZ,OAAO,CAAC,cAAc;YAAEM,OAAO;gBAACT;aAAI;QAAC;IAC/C,EAAE,OAAOgB,OAAO;QACd,IACEA,SACA,OAAOA,UAAU,YACjB,UAAUA,SACVA,MAAMC,IAAI,KAAK,oBACf;YACAC,IAAAA,SAAI,EACF,CAAC,yDAAyD,EAAEJ,eAAe,EAAE,CAAC;YAGhF,MAAMK,IAAAA,wCAAmB,EAACnB,KAAK;gBAAC;oBAAEoB,KAAK;gBAAa;aAAE,EAAE,MAAMC,KAAK,CACjE,CAACC;gBACC,IAAIA,OAAO,OAAOA,QAAQ,YAAY,aAAaA,KAAK;oBACtDC,QAAQP,KAAK,CACX,CAAC,uEAAuE,CAAC,GACvE,AAACM,IAAYE,OAAO,GACpB;gBAEN;gBACA,MAAMF;YACR;QAEJ;IACF;AACF;AAEA,eAAeG,YAAYzB,GAAW;IACpC,MAAM0B,KAAkCX,QACtCA,QAAQZ,OAAO,CAAC,cAAc;QAAEM,OAAO;YAACT;SAAI;IAAC;IAG/C,2DAA2D;IAC3D,8DAA8D;IAC9D,iBAAiB;IACjB,MAAM2B,eAAeD,GAAGE,cAAc,CACpC5B,KACA0B,GAAGG,GAAG,CAACC,UAAU,EACjB;IAGF,IAAI,CAACH,cAAc;QACjB,gEAAgE;QAChE,4DAA4D;QAC5D,OAAO,CAAC;IACV;IAEA,MAAMI,aAAaL,GAAGM,cAAc,CAACL,cAAcD,GAAGG,GAAG,CAACI,QAAQ;IAClE,MAAMC,oBAAoBR,GAAGS,0BAA0B,CACrDJ,WAAWK,MAAM,EACjBV,GAAGG,GAAG,EACN7B;IAGF,OAAOkC,kBAAkBG,OAAO;AAClC;AAEO,eAAevC,gBAAgB,EACpCwC,cAAc,EACdxB,cAAc,EACdd,GAAG,EAKJ;IACC,IAAIuC,aAAa;IACjB,IAAI;QACF,iDAAiD;QACjD,MAAM1B,sBAAsBb,KAAKc;QAEjC,MAAMb,kBAAkB,MAAMwB,YAAYzB;QAC1C,MAAMwC,aAAazC,kBAAkBC,KAAKC;QAE1C,MAAMwC,mBAAmB,MAAMR,IAAAA,kBAAQ,EAACK,gBAAgB;QACxD,qEAAqE;QACrE,oDAAoD;QACpD,MAAM,EAAEI,SAAS,EAAE,GAAG3B,QAAQ;QAC9B,MAAM,EAAEE,IAAI,EAAE,GAAG,MAAMyB,UAAUD,kBAAkBD;QAEnD,+CAA+C;QAC/C,IAAIvB,KAAK0B,QAAQ,CAAC,aAAa;YAC7BC,IAAAA,yBAAY,EAACJ;YACbD,aAAa;QACf;QAEA,yCAAyC;QACzC,OAAOM,IAAAA,8BAAiB,EAAC5B,MAAMd,IAAAA,iBAAO,EAACH,KAAK;IAC9C,EAAE,OAAOgB,OAAO;QACd,MAAMA;IACR,SAAU;QACR,IAAIuB,YAAY;YACdO,IAAAA,2BAAc;QAChB;IACF;AACF", "ignoreList": [0]}