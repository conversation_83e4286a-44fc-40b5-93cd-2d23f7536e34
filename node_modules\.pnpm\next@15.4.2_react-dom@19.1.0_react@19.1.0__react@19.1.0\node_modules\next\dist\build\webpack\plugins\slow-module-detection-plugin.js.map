{"version": 3, "sources": ["../../../../src/build/webpack/plugins/slow-module-detection-plugin.ts"], "sourcesContent": ["import type { Compiler, Module, Compilation } from 'webpack'\nimport type { CompilerNameValues } from '../../../shared/lib/constants'\nimport { yellow, green, blue } from '../../../lib/picocolors'\n\nconst PLUGIN_NAME = 'SlowModuleDetectionPlugin'\n\nconst TreeSymbols = {\n  VERTICAL_LINE: '│  ',\n  BRANCH: '├─ ',\n} as const\n\nconst PATH_TRUNCATION_LENGTH = 120\n\n// Matches node_modules paths, including pnpm-style paths\nconst NODE_MODULES_PATH_PATTERN = /node_modules(?:\\/\\.pnpm)?\\/(.*)/\n\ninterface ModuleBuildTimeAnalyzerOptions {\n  compilerType: CompilerNameValues\n  buildTimeThresholdMs: number\n}\n\nconst getModuleIdentifier = (module: Module): string => {\n  const debugId = module.debugId\n  return String(debugId)\n}\n\nconst getModuleDisplayName = (module: Module): string | undefined => {\n  const resourcePath =\n    'resource' in module && typeof module.resource === 'string'\n      ? module.resource\n      : undefined\n\n  if (!resourcePath) {\n    return undefined\n  }\n\n  let displayPath = resourcePath.replace(process.cwd(), '.')\n\n  const nodeModulesMatch = displayPath.match(NODE_MODULES_PATH_PATTERN)\n  if (nodeModulesMatch) {\n    return nodeModulesMatch[1]\n  }\n\n  return displayPath\n}\n\n/**\n * Truncates a path to a maximum length. If the path exceeds this length,\n * it will be truncated in the middle and replaced with '...'.\n */\nfunction truncatePath(path: string, maxLength: number): string {\n  // If the path length is within the limit, return it as is\n  if (path.length <= maxLength) return path\n\n  // Calculate the available length for the start and end segments after accounting for '...'\n  const availableLength = maxLength - 3\n  const startSegmentLength = Math.ceil(availableLength / 2)\n  const endSegmentLength = Math.floor(availableLength / 2)\n\n  // Extract the start and end segments of the path\n  const startSegment = path.slice(0, startSegmentLength)\n  const endSegment = path.slice(-endSegmentLength)\n\n  // Return the truncated path with '...' in the middle\n  return `${startSegment}...${endSegment}`\n}\n\nclass ModuleBuildTimeAnalyzer {\n  private pendingModules: Module[] = []\n  private modules = new Map<string, Module>()\n  private moduleParents = new Map<Module, Module>()\n  private moduleChildren = new Map<Module, Map<string, Module>>()\n  private isFinalized = false\n  private buildTimeThresholdMs: number\n  private moduleBuildTimes = new WeakMap<Module, number>()\n\n  constructor(private options: ModuleBuildTimeAnalyzerOptions) {\n    this.buildTimeThresholdMs = options.buildTimeThresholdMs\n  }\n\n  recordModuleBuildTime(module: Module, duration: number) {\n    // Webpack guarantees that no more modules will be built after finishModules hook is called,\n    // where we generate the report. This check is just a defensive measure.\n    if (this.isFinalized) {\n      throw new Error(\n        `Invariant (SlowModuleDetectionPlugin): Module is recorded after the report is generated. This is a Next.js internal bug.`\n      )\n    }\n\n    if (duration < this.buildTimeThresholdMs) {\n      return // Skip fast modules\n    }\n\n    this.moduleBuildTimes.set(module, duration)\n    this.pendingModules.push(module)\n  }\n\n  /**\n   * For each slow module, traverses up the dependency chain to find all ancestor modules.\n   * Builds a directed graph where:\n   * 1. Each slow module and its ancestors become nodes\n   * 2. Edges represent \"imported by\" relationships\n   * 3. Root nodes are entry points with no parents\n   *\n   * The resulting graph allows us to visualize the import chains that led to slow builds.\n   */\n  private prepareReport(compilation: Compilation) {\n    for (const module of this.pendingModules) {\n      const chain = new Set<Module>()\n\n      // Walk up the module graph until we hit a root module (no issuer) to populate the chain\n      {\n        let currentModule = module\n        chain.add(currentModule)\n        while (true) {\n          const issuerModule = compilation.moduleGraph.getIssuer(currentModule)\n          if (!issuerModule) break\n          if (chain.has(issuerModule)) {\n            throw new Error(\n              `Invariant (SlowModuleDetectionPlugin): Circular dependency detected in module graph. This is a Next.js internal bug.`\n            )\n          }\n          chain.add(issuerModule)\n          currentModule = issuerModule\n        }\n      }\n\n      // Add all visited modules to our graph and create parent-child relationships\n      let previousModule: Module | null = null\n      for (const currentModule of chain) {\n        const moduleId = getModuleIdentifier(currentModule)\n        if (!this.modules.has(moduleId)) {\n          this.modules.set(moduleId, currentModule)\n        }\n\n        if (previousModule) {\n          this.moduleParents.set(previousModule, currentModule)\n\n          let parentChildren = this.moduleChildren.get(currentModule)\n          if (!parentChildren) {\n            parentChildren = new Map()\n            this.moduleChildren.set(currentModule, parentChildren)\n          }\n          parentChildren.set(\n            getModuleIdentifier(previousModule),\n            previousModule\n          )\n        }\n\n        previousModule = currentModule\n      }\n    }\n    this.isFinalized = true\n  }\n\n  generateReport(compilation: Compilation) {\n    if (!this.isFinalized) {\n      this.prepareReport(compilation)\n    }\n\n    // Find root modules (those with no parents)\n    const rootModules = [...this.modules.values()].filter(\n      (node) => !this.moduleParents.has(node)\n    )\n\n    const formatModuleNode = (node: Module, depth: number): string => {\n      const moduleName = getModuleDisplayName(node) || ''\n\n      if (!moduleName) {\n        return formatChildModules(node, depth)\n      }\n\n      const prefix =\n        ' ' + TreeSymbols.VERTICAL_LINE.repeat(depth) + TreeSymbols.BRANCH\n\n      const moduleText = blue(\n        truncatePath(moduleName, PATH_TRUNCATION_LENGTH - prefix.length)\n      )\n\n      const buildTimeMs = this.moduleBuildTimes.get(node)\n      const duration = buildTimeMs\n        ? yellow(` (${Math.ceil(buildTimeMs)}ms)`)\n        : ''\n\n      return (\n        prefix +\n        moduleText +\n        duration +\n        '\\n' +\n        formatChildModules(node, depth + 1)\n      )\n    }\n\n    const formatChildModules = (node: Module, depth: number): string => {\n      const children = this.moduleChildren.get(node)\n      if (!children) return ''\n\n      return [...children]\n        .map(([_, child]) => formatModuleNode(child, depth))\n        .join('')\n    }\n\n    const report = rootModules.map((root) => formatModuleNode(root, 0)).join('')\n\n    if (report) {\n      console.log(\n        green(\n          `🐌 Detected slow modules while compiling ${this.options.compilerType}:`\n        ) +\n          '\\n' +\n          report\n      )\n    }\n  }\n}\n\nexport default class SlowModuleDetectionPlugin {\n  constructor(private options: ModuleBuildTimeAnalyzerOptions) {}\n\n  apply = (compiler: Compiler) => {\n    compiler.hooks.compilation.tap(PLUGIN_NAME, (compilation) => {\n      const analyzer = new ModuleBuildTimeAnalyzer(this.options)\n      const moduleBuildStartTimes = new WeakMap<Module, number>()\n\n      compilation.hooks.buildModule.tap(PLUGIN_NAME, (module) => {\n        moduleBuildStartTimes.set(module, performance.now())\n      })\n\n      compilation.hooks.succeedModule.tap(PLUGIN_NAME, (module) => {\n        const startTime = moduleBuildStartTimes.get(module)\n        if (!startTime) {\n          throw new Error(\n            `Invariant (SlowModuleDetectionPlugin): Unable to find the start time for a module build. This is a Next.js internal bug.`\n          )\n        }\n        analyzer.recordModuleBuildTime(module, performance.now() - startTime)\n      })\n\n      compilation.hooks.finishModules.tap(PLUGIN_NAME, () => {\n        analyzer.generateReport(compilation)\n      })\n    })\n  }\n}\n"], "names": ["SlowModuleDetectionPlugin", "PLUGIN_NAME", "TreeSymbols", "VERTICAL_LINE", "BRANCH", "PATH_TRUNCATION_LENGTH", "NODE_MODULES_PATH_PATTERN", "getModuleIdentifier", "module", "debugId", "String", "getModuleDisplayName", "resourcePath", "resource", "undefined", "displayPath", "replace", "process", "cwd", "nodeModulesMatch", "match", "truncatePath", "path", "max<PERSON><PERSON><PERSON>", "length", "availableLength", "startSegmentLength", "Math", "ceil", "endSegmentLength", "floor", "startSegment", "slice", "endSegment", "ModuleBuildTimeAnalyzer", "constructor", "options", "pendingModules", "modules", "Map", "moduleParents", "module<PERSON><PERSON><PERSON><PERSON>", "isFinalized", "moduleBuildTimes", "WeakMap", "buildTimeThresholdMs", "recordModuleBuildTime", "duration", "Error", "set", "push", "prepareReport", "compilation", "chain", "Set", "currentModule", "add", "issuerModule", "moduleGraph", "get<PERSON><PERSON><PERSON>", "has", "previousModule", "moduleId", "parent<PERSON><PERSON><PERSON><PERSON>", "get", "generateReport", "rootModules", "values", "filter", "node", "formatModuleNode", "depth", "moduleName", "formatChildModules", "prefix", "repeat", "moduleText", "blue", "buildTimeMs", "yellow", "children", "map", "_", "child", "join", "report", "root", "console", "log", "green", "compilerType", "apply", "compiler", "hooks", "tap", "analyzer", "moduleBuildStartTimes", "buildModule", "performance", "now", "succeedModule", "startTime", "finishModules"], "mappings": ";;;;+BAwNA;;;eAAqBA;;;4BAtNe;AAEpC,MAAMC,cAAc;AAEpB,MAAMC,cAAc;IAClBC,eAAe;IACfC,QAAQ;AACV;AAEA,MAAMC,yBAAyB;AAE/B,yDAAyD;AACzD,MAAMC,4BAA4B;AAOlC,MAAMC,sBAAsB,CAACC;IAC3B,MAAMC,UAAUD,OAAOC,OAAO;IAC9B,OAAOC,OAAOD;AAChB;AAEA,MAAME,uBAAuB,CAACH;IAC5B,MAAMI,eACJ,cAAcJ,UAAU,OAAOA,OAAOK,QAAQ,KAAK,WAC/CL,OAAOK,QAAQ,GACfC;IAEN,IAAI,CAACF,cAAc;QACjB,OAAOE;IACT;IAEA,IAAIC,cAAcH,aAAaI,OAAO,CAACC,QAAQC,GAAG,IAAI;IAEtD,MAAMC,mBAAmBJ,YAAYK,KAAK,CAACd;IAC3C,IAAIa,kBAAkB;QACpB,OAAOA,gBAAgB,CAAC,EAAE;IAC5B;IAEA,OAAOJ;AACT;AAEA;;;CAGC,GACD,SAASM,aAAaC,IAAY,EAAEC,SAAiB;IACnD,0DAA0D;IAC1D,IAAID,KAAKE,MAAM,IAAID,WAAW,OAAOD;IAErC,2FAA2F;IAC3F,MAAMG,kBAAkBF,YAAY;IACpC,MAAMG,qBAAqBC,KAAKC,IAAI,CAACH,kBAAkB;IACvD,MAAMI,mBAAmBF,KAAKG,KAAK,CAACL,kBAAkB;IAEtD,iDAAiD;IACjD,MAAMM,eAAeT,KAAKU,KAAK,CAAC,GAAGN;IACnC,MAAMO,aAAaX,KAAKU,KAAK,CAAC,CAACH;IAE/B,qDAAqD;IACrD,OAAO,GAAGE,aAAa,GAAG,EAAEE,YAAY;AAC1C;AAEA,MAAMC;IASJC,YAAY,AAAQC,OAAuC,CAAE;aAAzCA,UAAAA;aARZC,iBAA2B,EAAE;aAC7BC,UAAU,IAAIC;aACdC,gBAAgB,IAAID;aACpBE,iBAAiB,IAAIF;aACrBG,cAAc;aAEdC,mBAAmB,IAAIC;QAG7B,IAAI,CAACC,oBAAoB,GAAGT,QAAQS,oBAAoB;IAC1D;IAEAC,sBAAsBtC,MAAc,EAAEuC,QAAgB,EAAE;QACtD,4FAA4F;QAC5F,wEAAwE;QACxE,IAAI,IAAI,CAACL,WAAW,EAAE;YACpB,MAAM,qBAEL,CAFK,IAAIM,MACR,CAAC,wHAAwH,CAAC,GADtH,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAID,WAAW,IAAI,CAACF,oBAAoB,EAAE;YACxC,QAAO,oBAAoB;QAC7B;QAEA,IAAI,CAACF,gBAAgB,CAACM,GAAG,CAACzC,QAAQuC;QAClC,IAAI,CAACV,cAAc,CAACa,IAAI,CAAC1C;IAC3B;IAEA;;;;;;;;GAQC,GACD,AAAQ2C,cAAcC,WAAwB,EAAE;QAC9C,KAAK,MAAM5C,UAAU,IAAI,CAAC6B,cAAc,CAAE;YACxC,MAAMgB,QAAQ,IAAIC;YAElB,wFAAwF;YACxF;gBACE,IAAIC,gBAAgB/C;gBACpB6C,MAAMG,GAAG,CAACD;gBACV,MAAO,KAAM;oBACX,MAAME,eAAeL,YAAYM,WAAW,CAACC,SAAS,CAACJ;oBACvD,IAAI,CAACE,cAAc;oBACnB,IAAIJ,MAAMO,GAAG,CAACH,eAAe;wBAC3B,MAAM,qBAEL,CAFK,IAAIT,MACR,CAAC,oHAAoH,CAAC,GADlH,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBACAK,MAAMG,GAAG,CAACC;oBACVF,gBAAgBE;gBAClB;YACF;YAEA,6EAA6E;YAC7E,IAAII,iBAAgC;YACpC,KAAK,MAAMN,iBAAiBF,MAAO;gBACjC,MAAMS,WAAWvD,oBAAoBgD;gBACrC,IAAI,CAAC,IAAI,CAACjB,OAAO,CAACsB,GAAG,CAACE,WAAW;oBAC/B,IAAI,CAACxB,OAAO,CAACW,GAAG,CAACa,UAAUP;gBAC7B;gBAEA,IAAIM,gBAAgB;oBAClB,IAAI,CAACrB,aAAa,CAACS,GAAG,CAACY,gBAAgBN;oBAEvC,IAAIQ,iBAAiB,IAAI,CAACtB,cAAc,CAACuB,GAAG,CAACT;oBAC7C,IAAI,CAACQ,gBAAgB;wBACnBA,iBAAiB,IAAIxB;wBACrB,IAAI,CAACE,cAAc,CAACQ,GAAG,CAACM,eAAeQ;oBACzC;oBACAA,eAAed,GAAG,CAChB1C,oBAAoBsD,iBACpBA;gBAEJ;gBAEAA,iBAAiBN;YACnB;QACF;QACA,IAAI,CAACb,WAAW,GAAG;IACrB;IAEAuB,eAAeb,WAAwB,EAAE;QACvC,IAAI,CAAC,IAAI,CAACV,WAAW,EAAE;YACrB,IAAI,CAACS,aAAa,CAACC;QACrB;QAEA,4CAA4C;QAC5C,MAAMc,cAAc;eAAI,IAAI,CAAC5B,OAAO,CAAC6B,MAAM;SAAG,CAACC,MAAM,CACnD,CAACC,OAAS,CAAC,IAAI,CAAC7B,aAAa,CAACoB,GAAG,CAACS;QAGpC,MAAMC,mBAAmB,CAACD,MAAcE;YACtC,MAAMC,aAAa7D,qBAAqB0D,SAAS;YAEjD,IAAI,CAACG,YAAY;gBACf,OAAOC,mBAAmBJ,MAAME;YAClC;YAEA,MAAMG,SACJ,MAAMxE,YAAYC,aAAa,CAACwE,MAAM,CAACJ,SAASrE,YAAYE,MAAM;YAEpE,MAAMwE,aAAaC,IAAAA,gBAAI,EACrBxD,aAAamD,YAAYnE,yBAAyBqE,OAAOlD,MAAM;YAGjE,MAAMsD,cAAc,IAAI,CAACnC,gBAAgB,CAACqB,GAAG,CAACK;YAC9C,MAAMtB,WAAW+B,cACbC,IAAAA,kBAAM,EAAC,CAAC,EAAE,EAAEpD,KAAKC,IAAI,CAACkD,aAAa,GAAG,CAAC,IACvC;YAEJ,OACEJ,SACAE,aACA7B,WACA,OACA0B,mBAAmBJ,MAAME,QAAQ;QAErC;QAEA,MAAME,qBAAqB,CAACJ,MAAcE;YACxC,MAAMS,WAAW,IAAI,CAACvC,cAAc,CAACuB,GAAG,CAACK;YACzC,IAAI,CAACW,UAAU,OAAO;YAEtB,OAAO;mBAAIA;aAAS,CACjBC,GAAG,CAAC,CAAC,CAACC,GAAGC,MAAM,GAAKb,iBAAiBa,OAAOZ,QAC5Ca,IAAI,CAAC;QACV;QAEA,MAAMC,SAASnB,YAAYe,GAAG,CAAC,CAACK,OAAShB,iBAAiBgB,MAAM,IAAIF,IAAI,CAAC;QAEzE,IAAIC,QAAQ;YACVE,QAAQC,GAAG,CACTC,IAAAA,iBAAK,EACH,CAAC,yCAAyC,EAAE,IAAI,CAACrD,OAAO,CAACsD,YAAY,CAAC,CAAC,CAAC,IAExE,OACAL;QAEN;IACF;AACF;AAEe,MAAMrF;IACnBmC,YAAY,AAAQC,OAAuC,CAAE;aAAzCA,UAAAA;aAEpBuD,QAAQ,CAACC;YACPA,SAASC,KAAK,CAACzC,WAAW,CAAC0C,GAAG,CAAC7F,aAAa,CAACmD;gBAC3C,MAAM2C,WAAW,IAAI7D,wBAAwB,IAAI,CAACE,OAAO;gBACzD,MAAM4D,wBAAwB,IAAIpD;gBAElCQ,YAAYyC,KAAK,CAACI,WAAW,CAACH,GAAG,CAAC7F,aAAa,CAACO;oBAC9CwF,sBAAsB/C,GAAG,CAACzC,QAAQ0F,YAAYC,GAAG;gBACnD;gBAEA/C,YAAYyC,KAAK,CAACO,aAAa,CAACN,GAAG,CAAC7F,aAAa,CAACO;oBAChD,MAAM6F,YAAYL,sBAAsBhC,GAAG,CAACxD;oBAC5C,IAAI,CAAC6F,WAAW;wBACd,MAAM,qBAEL,CAFK,IAAIrD,MACR,CAAC,wHAAwH,CAAC,GADtH,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBACA+C,SAASjD,qBAAqB,CAACtC,QAAQ0F,YAAYC,GAAG,KAAKE;gBAC7D;gBAEAjD,YAAYyC,KAAK,CAACS,aAAa,CAACR,GAAG,CAAC7F,aAAa;oBAC/C8F,SAAS9B,cAAc,CAACb;gBAC1B;YACF;QACF;IAzB8D;AA0BhE", "ignoreList": [0]}