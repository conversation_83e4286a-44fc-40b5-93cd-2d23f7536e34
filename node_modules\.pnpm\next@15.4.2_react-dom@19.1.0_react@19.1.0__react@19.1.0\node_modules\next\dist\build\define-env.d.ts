import type { NextConfigComplete } from '../server/config-shared';
import type { MiddlewareMatcher } from './analysis/get-page-static-info';
import type { Rewrite } from '../lib/load-custom-routes';
type BloomFilter = ReturnType<import('../shared/lib/bloom-filter').BloomFilter['export']>;
export interface DefineEnvOptions {
    isTurbopack: boolean;
    clientRouterFilters?: {
        staticFilter: BloomFilter;
        dynamicFilter: BloomFilter;
    };
    config: NextConfigComplete;
    dev: boolean;
    distDir: string;
    projectPath: string;
    fetchCacheKeyPrefix: string | undefined;
    hasRewrites: boolean;
    isClient: boolean;
    isEdgeServer: boolean;
    isNodeServer: boolean;
    middlewareMatchers: MiddlewareMatcher[] | undefined;
    omitNonDeterministic?: boolean;
    rewrites: {
        beforeFiles: Rewrite[];
        afterFiles: Rewrite[];
        fallback: Rewrite[];
    };
}
interface SerializedDefineEnv {
    [key: string]: string;
}
export declare function getDefineEnv({ isTurbopack, clientRouterFilters, config, dev, distDir, projectPath, fetchCacheKeyPrefix, hasRewrites, isClient, isEdgeServer, isNodeServer, middlewareMatchers, omitNonDeterministic, rewrites, }: DefineEnvOptions): SerializedDefineEnv;
export {};
