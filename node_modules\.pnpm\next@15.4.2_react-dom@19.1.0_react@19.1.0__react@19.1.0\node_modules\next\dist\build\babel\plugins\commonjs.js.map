{"version": 3, "sources": ["../../../../src/build/babel/plugins/commonjs.ts"], "sourcesContent": ["import type { NodePath, types } from 'next/dist/compiled/babel/core'\nimport type { PluginObj } from 'next/dist/compiled/babel/core'\nimport commonjsPlugin from 'next/dist/compiled/babel/plugin-transform-modules-commonjs'\n\n// Handle module.exports in user code\nexport default function CommonJSModulePlugin(...args: any): PluginObj {\n  const commonjs = commonjsPlugin(...args)\n  return {\n    visitor: {\n      Program: {\n        exit(path: NodePath<types.Program>, state) {\n          let foundModuleExports = false\n          path.traverse({\n            MemberExpression(expressionPath: any) {\n              if (expressionPath.node.object.name !== 'module') return\n              if (expressionPath.node.property.name !== 'exports') return\n              foundModuleExports = true\n            },\n          })\n\n          if (!foundModuleExports) {\n            return\n          }\n\n          commonjs.visitor.Program.exit.call(this, path, state)\n        },\n      },\n    },\n  }\n}\n"], "names": ["CommonJSModulePlugin", "args", "commonjs", "commonjsPlugin", "visitor", "Program", "exit", "path", "state", "foundModuleExports", "traverse", "MemberExpression", "expressionPath", "node", "object", "name", "property", "call"], "mappings": ";;;;+BAIA,qCAAqC;AACrC;;;eAAwBA;;;uFAHG;;;;;;AAGZ,SAASA,qBAAqB,GAAGC,IAAS;IACvD,MAAMC,WAAWC,IAAAA,uCAAc,KAAIF;IACnC,OAAO;QACLG,SAAS;YACPC,SAAS;gBACPC,MAAKC,IAA6B,EAAEC,KAAK;oBACvC,IAAIC,qBAAqB;oBACzBF,KAAKG,QAAQ,CAAC;wBACZC,kBAAiBC,cAAmB;4BAClC,IAAIA,eAAeC,IAAI,CAACC,MAAM,CAACC,IAAI,KAAK,UAAU;4BAClD,IAAIH,eAAeC,IAAI,CAACG,QAAQ,CAACD,IAAI,KAAK,WAAW;4BACrDN,qBAAqB;wBACvB;oBACF;oBAEA,IAAI,CAACA,oBAAoB;wBACvB;oBACF;oBAEAP,SAASE,OAAO,CAACC,OAAO,CAACC,IAAI,CAACW,IAAI,CAAC,IAAI,EAAEV,MAAMC;gBACjD;YACF;QACF;IACF;AACF", "ignoreList": [0]}