{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-app-loader/create-app-route-code.ts"], "sourcesContent": ["import path from 'path'\nimport { stringify } from 'querystring'\nimport { WEBPACK_RESOURCE_QUERIES } from '../../../../lib/constants'\nimport {\n  DEFAULT_METADATA_ROUTE_EXTENSIONS,\n  isMetadataRouteFile,\n} from '../../../../lib/metadata/is-metadata-route'\nimport type { NextConfig } from '../../../../server/config-shared'\nimport { AppBundlePathNormalizer } from '../../../../server/normalizers/built/app/app-bundle-path-normalizer'\nimport { AppPathnameNormalizer } from '../../../../server/normalizers/built/app/app-pathname-normalizer'\nimport { loadEntrypoint } from '../../../load-entrypoint'\nimport type { PageExtensions } from '../../../page-extensions-type'\nimport { getFilenameAndExtension } from '../next-metadata-route-loader'\n\nexport async function createAppRouteCode({\n  appDir,\n  name,\n  page,\n  pagePath,\n  resolveAppRoute,\n  pageExtensions,\n  nextConfigOutput,\n}: {\n  appDir: string\n  name: string\n  page: string\n  pagePath: string\n  resolveAppRoute: (\n    pathname: string\n  ) => Promise<string | undefined> | string | undefined\n  pageExtensions: PageExtensions\n  nextConfigOutput: NextConfig['output']\n}): Promise<string> {\n  // routePath is the path to the route handler file,\n  // but could be aliased e.g. private-next-app-dir/favicon.ico\n  const routePath = pagePath.replace(/[\\\\/]/, '/')\n\n  // This, when used with the resolver will give us the pathname to the built\n  // route handler file.\n  let resolvedPagePath = await resolveAppRoute(routePath)\n  if (!resolvedPagePath) {\n    throw new Error(\n      `Invariant: could not resolve page path for ${name} at ${routePath}`\n    )\n  }\n\n  // If this is a metadata route file, then we need to use the metadata-loader\n  // for the route to ensure that the route is generated.\n  const fileBaseName = path.parse(resolvedPagePath).name\n  const appDirRelativePath = resolvedPagePath.slice(appDir.length)\n  const isMetadataEntryFile = isMetadataRouteFile(\n    appDirRelativePath,\n    DEFAULT_METADATA_ROUTE_EXTENSIONS,\n    true\n  )\n  if (isMetadataEntryFile) {\n    const { ext } = getFilenameAndExtension(resolvedPagePath)\n    const isDynamicRouteExtension = pageExtensions.includes(ext)\n\n    resolvedPagePath = `next-metadata-route-loader?${stringify({\n      filePath: resolvedPagePath,\n      isDynamicRouteExtension: isDynamicRouteExtension ? '1' : '0',\n    })}!?${WEBPACK_RESOURCE_QUERIES.metadataRoute}`\n  }\n\n  const pathname = new AppPathnameNormalizer().normalize(page)\n  const bundlePath = new AppBundlePathNormalizer().normalize(page)\n\n  return await loadEntrypoint(\n    'app-route',\n    {\n      VAR_USERLAND: resolvedPagePath,\n      VAR_DEFINITION_PAGE: page,\n      VAR_DEFINITION_PATHNAME: pathname,\n      VAR_DEFINITION_FILENAME: fileBaseName,\n      VAR_DEFINITION_BUNDLE_PATH: bundlePath,\n      VAR_RESOLVED_PAGE_PATH: resolvedPagePath,\n    },\n    {\n      nextConfigOutput: JSON.stringify(nextConfigOutput),\n    }\n  )\n}\n"], "names": ["createAppRouteCode", "appDir", "name", "page", "pagePath", "resolveAppRoute", "pageExtensions", "nextConfigOutput", "routePath", "replace", "resolvedPagePath", "Error", "fileBaseName", "path", "parse", "appDirRelativePath", "slice", "length", "isMetadataEntryFile", "isMetadataRouteFile", "DEFAULT_METADATA_ROUTE_EXTENSIONS", "ext", "getFilenameAndExtension", "isDynamicRouteExtension", "includes", "stringify", "filePath", "WEBPACK_RESOURCE_QUERIES", "metadataRoute", "pathname", "AppPathnameNormalizer", "normalize", "bundlePath", "AppBundlePathNormalizer", "loadEntrypoint", "VAR_USERLAND", "VAR_DEFINITION_PAGE", "VAR_DEFINITION_PATHNAME", "VAR_DEFINITION_FILENAME", "VAR_DEFINITION_BUNDLE_PATH", "VAR_RESOLVED_PAGE_PATH", "JSON"], "mappings": ";;;;+BAcsBA;;;eAAAA;;;6DAdL;6BACS;2BACe;iCAIlC;yCAEiC;uCACF;gCACP;yCAES;;;;;;AAEjC,eAAeA,mBAAmB,EACvCC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,eAAe,EACfC,cAAc,EACdC,gBAAgB,EAWjB;IACC,mDAAmD;IACnD,6DAA6D;IAC7D,MAAMC,YAAYJ,SAASK,OAAO,CAAC,SAAS;IAE5C,2EAA2E;IAC3E,sBAAsB;IACtB,IAAIC,mBAAmB,MAAML,gBAAgBG;IAC7C,IAAI,CAACE,kBAAkB;QACrB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,2CAA2C,EAAET,KAAK,IAAI,EAAEM,WAAW,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4EAA4E;IAC5E,uDAAuD;IACvD,MAAMI,eAAeC,aAAI,CAACC,KAAK,CAACJ,kBAAkBR,IAAI;IACtD,MAAMa,qBAAqBL,iBAAiBM,KAAK,CAACf,OAAOgB,MAAM;IAC/D,MAAMC,sBAAsBC,IAAAA,oCAAmB,EAC7CJ,oBACAK,kDAAiC,EACjC;IAEF,IAAIF,qBAAqB;QACvB,MAAM,EAAEG,GAAG,EAAE,GAAGC,IAAAA,gDAAuB,EAACZ;QACxC,MAAMa,0BAA0BjB,eAAekB,QAAQ,CAACH;QAExDX,mBAAmB,CAAC,2BAA2B,EAAEe,IAAAA,sBAAS,EAAC;YACzDC,UAAUhB;YACVa,yBAAyBA,0BAA0B,MAAM;QAC3D,GAAG,EAAE,EAAEI,mCAAwB,CAACC,aAAa,EAAE;IACjD;IAEA,MAAMC,WAAW,IAAIC,4CAAqB,GAAGC,SAAS,CAAC5B;IACvD,MAAM6B,aAAa,IAAIC,gDAAuB,GAAGF,SAAS,CAAC5B;IAE3D,OAAO,MAAM+B,IAAAA,8BAAc,EACzB,aACA;QACEC,cAAczB;QACd0B,qBAAqBjC;QACrBkC,yBAAyBR;QACzBS,yBAAyB1B;QACzB2B,4BAA4BP;QAC5BQ,wBAAwB9B;IAC1B,GACA;QACEH,kBAAkBkC,KAAKhB,SAAS,CAAClB;IACnC;AAEJ", "ignoreList": [0]}