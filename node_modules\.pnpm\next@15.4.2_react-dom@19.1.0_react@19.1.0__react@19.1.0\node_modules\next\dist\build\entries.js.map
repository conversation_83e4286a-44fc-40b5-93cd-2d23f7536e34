{"version": 3, "sources": ["../../src/build/entries.ts"], "sourcesContent": ["import type { ClientPagesLoaderOptions } from './webpack/loaders/next-client-pages-loader'\nimport type { MiddlewareLoaderOptions } from './webpack/loaders/next-middleware-loader'\nimport type { EdgeSSRLoaderQuery } from './webpack/loaders/next-edge-ssr-loader'\nimport type { EdgeAppRouteLoaderQuery } from './webpack/loaders/next-edge-app-route-loader'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type {\n  MiddlewareConfig,\n  MiddlewareMatcher,\n  PageStaticInfo,\n} from './analysis/get-page-static-info'\nimport * as Log from './output/log'\nimport type { LoadedEnvFiles } from '@next/env'\nimport type { AppLoaderOptions } from './webpack/loaders/next-app-loader'\n\nimport { posix, join, dirname, extname, normalize } from 'path'\nimport { stringify } from 'querystring'\nimport fs from 'fs'\nimport {\n  PAGES_DIR_ALIAS,\n  ROOT_DIR_ALIAS,\n  APP_DIR_ALIAS,\n  WEBPACK_LAYERS,\n  INSTRUMENTATION_HOOK_FILENAME,\n} from '../lib/constants'\nimport { isAPIRoute } from '../lib/is-api-route'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport {\n  APP_CLIENT_INTERNALS,\n  RSC_MODULE_TYPES,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../shared/lib/constants'\nimport {\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  COMPILER_NAMES,\n  EDGE_RUNTIME_WEBPACK,\n} from '../shared/lib/constants'\nimport type { CompilerNameValues } from '../shared/lib/constants'\nimport type { __ApiPreviewProps } from '../server/api-utils'\nimport {\n  isMiddlewareFile,\n  isMiddlewareFilename,\n  isInstrumentationHookFile,\n  isInstrumentationHookFilename,\n  reduceAppConfig,\n} from './utils'\nimport {\n  getAppPageStaticInfo,\n  getPageStaticInfo,\n} from './analysis/get-page-static-info'\nimport { normalizePathSep } from '../shared/lib/page-path/normalize-path-sep'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport type { ServerRuntime } from '../types'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport { encodeMatchers } from './webpack/loaders/next-middleware-loader'\nimport type { EdgeFunctionLoaderOptions } from './webpack/loaders/next-edge-function-loader'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport {\n  normalizeMetadataPageToRoute,\n  normalizeMetadataRoute,\n} from '../lib/metadata/get-metadata-route'\nimport { getRouteLoaderEntry } from './webpack/loaders/next-route-loader'\nimport {\n  isInternalComponent,\n  isNonRoutePagesPage,\n} from '../lib/is-internal-component'\nimport { isMetadataRouteFile } from '../lib/metadata/is-metadata-route'\nimport { RouteKind } from '../server/route-kind'\nimport { encodeToBase64 } from './webpack/loaders/utils'\nimport { normalizeCatchAllRoutes } from './normalize-catchall-routes'\nimport type { PageExtensions } from './page-extensions-type'\nimport type { MappedPages } from './build-context'\nimport { PAGE_TYPES } from '../lib/page-types'\nimport { isAppPageRoute } from '../lib/is-app-page-route'\n\nexport function sortByPageExts(pageExtensions: PageExtensions) {\n  return (a: string, b: string) => {\n    // prioritize entries according to pageExtensions order\n    // for consistency as fs order can differ across systems\n    // NOTE: this is reversed so preferred comes last and\n    // overrides prior\n    const aExt = extname(a)\n    const bExt = extname(b)\n\n    const aNoExt = a.substring(0, a.length - aExt.length)\n    const bNoExt = a.substring(0, b.length - bExt.length)\n\n    if (aNoExt !== bNoExt) return 0\n\n    // find extension index (skip '.' as pageExtensions doesn't have it)\n    const aExtIndex = pageExtensions.indexOf(aExt.substring(1))\n    const bExtIndex = pageExtensions.indexOf(bExt.substring(1))\n\n    return bExtIndex - aExtIndex\n  }\n}\n\nexport async function getStaticInfoIncludingLayouts({\n  isInsideAppDir,\n  pageExtensions,\n  pageFilePath,\n  appDir,\n  config: nextConfig,\n  isDev,\n  page,\n}: {\n  isInsideAppDir: boolean\n  pageExtensions: PageExtensions\n  pageFilePath: string\n  appDir: string | undefined\n  config: NextConfigComplete\n  isDev: boolean | undefined\n  page: string\n}): Promise<PageStaticInfo> {\n  // TODO: sync types for pages: PAGE_TYPES, ROUTER_TYPE, 'app' | 'pages', etc.\n  const pageType = isInsideAppDir ? PAGE_TYPES.APP : PAGE_TYPES.PAGES\n\n  const pageStaticInfo = await getPageStaticInfo({\n    nextConfig,\n    pageFilePath,\n    isDev,\n    page,\n    pageType,\n  })\n\n  if (pageStaticInfo.type === PAGE_TYPES.PAGES || !appDir) {\n    return pageStaticInfo\n  }\n\n  const segments = [pageStaticInfo]\n\n  // inherit from layout files only if it's a page route\n  if (isAppPageRoute(page)) {\n    const layoutFiles = []\n    const potentialLayoutFiles = pageExtensions.map((ext) => 'layout.' + ext)\n    let dir = dirname(pageFilePath)\n\n    // Uses startsWith to not include directories further up.\n    while (dir.startsWith(appDir)) {\n      for (const potentialLayoutFile of potentialLayoutFiles) {\n        const layoutFile = join(dir, potentialLayoutFile)\n        if (!fs.existsSync(layoutFile)) {\n          continue\n        }\n        layoutFiles.push(layoutFile)\n      }\n      // Walk up the directory tree\n      dir = join(dir, '..')\n    }\n\n    for (const layoutFile of layoutFiles) {\n      const layoutStaticInfo = await getAppPageStaticInfo({\n        nextConfig,\n        pageFilePath: layoutFile,\n        isDev,\n        page,\n        pageType: isInsideAppDir ? PAGE_TYPES.APP : PAGE_TYPES.PAGES,\n      })\n\n      segments.unshift(layoutStaticInfo)\n    }\n  }\n\n  const config = reduceAppConfig(segments)\n\n  return {\n    ...pageStaticInfo,\n    config,\n    runtime: config.runtime,\n    preferredRegion: config.preferredRegion,\n    maxDuration: config.maxDuration,\n  }\n}\n\ntype ObjectValue<T> = T extends { [key: string]: infer V } ? V : never\n\n/**\n * For a given page path removes the provided extensions.\n */\nexport function getPageFromPath(\n  pagePath: string,\n  pageExtensions: PageExtensions\n) {\n  let page = normalizePathSep(\n    pagePath.replace(new RegExp(`\\\\.+(${pageExtensions.join('|')})$`), '')\n  )\n\n  page = page.replace(/\\/index$/, '')\n\n  return page === '' ? '/' : page\n}\n\nexport function getPageFilePath({\n  absolutePagePath,\n  pagesDir,\n  appDir,\n  rootDir,\n}: {\n  absolutePagePath: string\n  pagesDir: string | undefined\n  appDir: string | undefined\n  rootDir: string\n}) {\n  if (absolutePagePath.startsWith(PAGES_DIR_ALIAS) && pagesDir) {\n    return absolutePagePath.replace(PAGES_DIR_ALIAS, pagesDir)\n  }\n\n  if (absolutePagePath.startsWith(APP_DIR_ALIAS) && appDir) {\n    return absolutePagePath.replace(APP_DIR_ALIAS, appDir)\n  }\n\n  if (absolutePagePath.startsWith(ROOT_DIR_ALIAS)) {\n    return absolutePagePath.replace(ROOT_DIR_ALIAS, rootDir)\n  }\n\n  return require.resolve(absolutePagePath)\n}\n\n/**\n * Creates a mapping of route to page file path for a given list of page paths.\n * For example ['/middleware.ts'] is turned into  { '/middleware': `${ROOT_DIR_ALIAS}/middleware.ts` }\n */\nexport async function createPagesMapping({\n  isDev,\n  pageExtensions,\n  pagePaths,\n  pagesType,\n  pagesDir,\n  appDir,\n}: {\n  isDev: boolean\n  pageExtensions: PageExtensions\n  pagePaths: string[]\n  pagesType: PAGE_TYPES\n  pagesDir: string | undefined\n  appDir: string | undefined\n}): Promise<MappedPages> {\n  const isAppRoute = pagesType === 'app'\n  const pages: MappedPages = {}\n  const promises = pagePaths.map<Promise<void>>(async (pagePath) => {\n    // Do not process .d.ts files as routes\n    if (pagePath.endsWith('.d.ts') && pageExtensions.includes('ts')) {\n      return\n    }\n\n    let pageKey = getPageFromPath(pagePath, pageExtensions)\n    if (isAppRoute) {\n      pageKey = pageKey.replace(/%5F/g, '_')\n      if (pageKey === '/not-found') {\n        pageKey = UNDERSCORE_NOT_FOUND_ROUTE_ENTRY\n      }\n    }\n\n    const normalizedPath = normalizePathSep(\n      join(\n        pagesType === 'pages'\n          ? PAGES_DIR_ALIAS\n          : pagesType === 'app'\n            ? APP_DIR_ALIAS\n            : ROOT_DIR_ALIAS,\n        pagePath\n      )\n    )\n\n    let route = pagesType === 'app' ? normalizeMetadataRoute(pageKey) : pageKey\n\n    if (\n      pagesType === 'app' &&\n      isMetadataRouteFile(pagePath, pageExtensions, true)\n    ) {\n      const filePath = join(appDir!, pagePath)\n      const staticInfo = await getPageStaticInfo({\n        nextConfig: {},\n        pageFilePath: filePath,\n        isDev,\n        page: pageKey,\n        pageType: pagesType,\n      })\n\n      route = normalizeMetadataPageToRoute(\n        route,\n        !!(staticInfo.generateImageMetadata || staticInfo.generateSitemaps)\n      )\n    }\n\n    pages[route] = normalizedPath\n  })\n\n  await Promise.all(promises)\n\n  switch (pagesType) {\n    case PAGE_TYPES.ROOT: {\n      return pages\n    }\n    case PAGE_TYPES.APP: {\n      const hasAppPages = Object.keys(pages).some((page) =>\n        page.endsWith('/page')\n      )\n      return {\n        // If there's any app pages existed, add a default /_not-found route as 404.\n        // If there's any custom /_not-found page, it will override the default one.\n        ...(hasAppPages && {\n          [UNDERSCORE_NOT_FOUND_ROUTE_ENTRY]: require.resolve(\n            'next/dist/client/components/builtin/global-not-found'\n          ),\n        }),\n        ...pages,\n      }\n    }\n    case PAGE_TYPES.PAGES: {\n      if (isDev) {\n        delete pages['/_app']\n        delete pages['/_error']\n        delete pages['/_document']\n      }\n\n      // In development we always alias these to allow Webpack to fallback to\n      // the correct source file so that HMR can work properly when a file is\n      // added or removed.\n      const root = isDev && pagesDir ? PAGES_DIR_ALIAS : 'next/dist/pages'\n\n      return {\n        '/_app': `${root}/_app`,\n        '/_error': `${root}/_error`,\n        '/_document': `${root}/_document`,\n        ...pages,\n      }\n    }\n    default: {\n      return {}\n    }\n  }\n}\n\nexport interface CreateEntrypointsParams {\n  buildId: string\n  config: NextConfigComplete\n  envFiles: LoadedEnvFiles\n  isDev?: boolean\n  pages: MappedPages\n  pagesDir?: string\n  previewMode: __ApiPreviewProps\n  rootDir: string\n  rootPaths?: MappedPages\n  appDir?: string\n  appPaths?: MappedPages\n  pageExtensions: PageExtensions\n  hasInstrumentationHook?: boolean\n}\n\nexport function getEdgeServerEntry(opts: {\n  rootDir: string\n  absolutePagePath: string\n  buildId: string\n  bundlePath: string\n  config: NextConfigComplete\n  isDev: boolean\n  isServerComponent: boolean\n  page: string\n  pages: MappedPages\n  middleware?: Partial<MiddlewareConfig>\n  pagesType: PAGE_TYPES\n  appDirLoader?: string\n  hasInstrumentationHook?: boolean\n  preferredRegion: string | string[] | undefined\n  middlewareConfig?: MiddlewareConfig\n}) {\n  if (\n    opts.pagesType === 'app' &&\n    isAppRouteRoute(opts.page) &&\n    opts.appDirLoader\n  ) {\n    const loaderParams: EdgeAppRouteLoaderQuery = {\n      absolutePagePath: opts.absolutePagePath,\n      page: opts.page,\n      appDirLoader: Buffer.from(opts.appDirLoader || '').toString('base64'),\n      nextConfig: Buffer.from(JSON.stringify(opts.config)).toString('base64'),\n      preferredRegion: opts.preferredRegion,\n      middlewareConfig: Buffer.from(\n        JSON.stringify(opts.middlewareConfig || {})\n      ).toString('base64'),\n      cacheHandlers: JSON.stringify(\n        opts.config.experimental.cacheHandlers || {}\n      ),\n    }\n\n    return {\n      import: `next-edge-app-route-loader?${stringify(loaderParams)}!`,\n      layer: WEBPACK_LAYERS.reactServerComponents,\n    }\n  }\n\n  if (isMiddlewareFile(opts.page)) {\n    const loaderParams: MiddlewareLoaderOptions = {\n      absolutePagePath: opts.absolutePagePath,\n      page: opts.page,\n      rootDir: opts.rootDir,\n      matchers: opts.middleware?.matchers\n        ? encodeMatchers(opts.middleware.matchers)\n        : '',\n      preferredRegion: opts.preferredRegion,\n      middlewareConfig: Buffer.from(\n        JSON.stringify(opts.middlewareConfig || {})\n      ).toString('base64'),\n    }\n\n    return {\n      import: `next-middleware-loader?${stringify(loaderParams)}!`,\n      layer: WEBPACK_LAYERS.middleware,\n    }\n  }\n\n  if (isAPIRoute(opts.page)) {\n    const loaderParams: EdgeFunctionLoaderOptions = {\n      absolutePagePath: opts.absolutePagePath,\n      page: opts.page,\n      rootDir: opts.rootDir,\n      preferredRegion: opts.preferredRegion,\n      middlewareConfig: Buffer.from(\n        JSON.stringify(opts.middlewareConfig || {})\n      ).toString('base64'),\n    }\n\n    return {\n      import: `next-edge-function-loader?${stringify(loaderParams)}!`,\n      layer: WEBPACK_LAYERS.apiEdge,\n    }\n  }\n\n  const loaderParams: EdgeSSRLoaderQuery = {\n    absolute500Path: opts.pages['/500'] || '',\n    absoluteAppPath: opts.pages['/_app'],\n    absoluteDocumentPath: opts.pages['/_document'],\n    absoluteErrorPath: opts.pages['/_error'],\n    absolutePagePath: opts.absolutePagePath,\n    dev: opts.isDev,\n    isServerComponent: opts.isServerComponent,\n    page: opts.page,\n    stringifiedConfig: Buffer.from(JSON.stringify(opts.config)).toString(\n      'base64'\n    ),\n    pagesType: opts.pagesType,\n    appDirLoader: Buffer.from(opts.appDirLoader || '').toString('base64'),\n    sriEnabled: !opts.isDev && !!opts.config.experimental.sri?.algorithm,\n    cacheHandler: opts.config.cacheHandler,\n    preferredRegion: opts.preferredRegion,\n    middlewareConfig: Buffer.from(\n      JSON.stringify(opts.middlewareConfig || {})\n    ).toString('base64'),\n    serverActions: opts.config.experimental.serverActions,\n    cacheHandlers: JSON.stringify(opts.config.experimental.cacheHandlers || {}),\n  }\n\n  return {\n    import: `next-edge-ssr-loader?${JSON.stringify(loaderParams)}!`,\n    // The Edge bundle includes the server in its entrypoint, so it has to\n    // be in the SSR layer — we later convert the page request to the RSC layer\n    // via a webpack rule.\n    layer: opts.appDirLoader ? WEBPACK_LAYERS.serverSideRendering : undefined,\n  }\n}\n\nexport function getInstrumentationEntry(opts: {\n  absolutePagePath: string\n  isEdgeServer: boolean\n  isDev: boolean\n}) {\n  // the '../' is needed to make sure the file is not chunked\n  const filename = `${\n    opts.isEdgeServer ? 'edge-' : opts.isDev ? '' : '../'\n  }${INSTRUMENTATION_HOOK_FILENAME}.js`\n\n  return {\n    import: opts.absolutePagePath,\n    filename,\n    layer: WEBPACK_LAYERS.instrument,\n  }\n}\n\nexport function getAppLoader() {\n  return process.env.BUILTIN_APP_LOADER\n    ? `builtin:next-app-loader`\n    : 'next-app-loader'\n}\n\nexport function getAppEntry(opts: Readonly<AppLoaderOptions>) {\n  if (process.env.NEXT_RSPACK && process.env.BUILTIN_APP_LOADER) {\n    ;(opts as any).projectRoot = normalize(join(__dirname, '../../..'))\n  }\n  return {\n    import: `${getAppLoader()}?${stringify(opts)}!`,\n    layer: WEBPACK_LAYERS.reactServerComponents,\n  }\n}\n\nexport function getClientEntry(opts: {\n  absolutePagePath: string\n  page: string\n}) {\n  const loaderOptions: ClientPagesLoaderOptions = {\n    absolutePagePath: opts.absolutePagePath,\n    page: opts.page,\n  }\n\n  const pageLoader = `next-client-pages-loader?${stringify(loaderOptions)}!`\n\n  // Make sure next/router is a dependency of _app or else chunk splitting\n  // might cause the router to not be able to load causing hydration\n  // to fail\n  return opts.page === '/_app'\n    ? [pageLoader, require.resolve('../client/router')]\n    : pageLoader\n}\n\nexport function runDependingOnPageType<T>(params: {\n  onClient: () => T\n  onEdgeServer: () => T\n  onServer: () => T\n  page: string\n  pageRuntime: ServerRuntime\n  pageType?: PAGE_TYPES\n}): void {\n  if (\n    params.pageType === PAGE_TYPES.ROOT &&\n    isInstrumentationHookFile(params.page)\n  ) {\n    params.onServer()\n    params.onEdgeServer()\n    return\n  }\n\n  if (isMiddlewareFile(params.page)) {\n    if (params.pageRuntime === 'nodejs') {\n      params.onServer()\n      return\n    } else {\n      params.onEdgeServer()\n      return\n    }\n  }\n\n  if (isAPIRoute(params.page)) {\n    if (isEdgeRuntime(params.pageRuntime)) {\n      params.onEdgeServer()\n      return\n    }\n\n    params.onServer()\n    return\n  }\n  if (params.page === '/_document') {\n    params.onServer()\n    return\n  }\n  if (\n    params.page === '/_app' ||\n    params.page === '/_error' ||\n    params.page === '/404' ||\n    params.page === '/500'\n  ) {\n    params.onClient()\n    params.onServer()\n    return\n  }\n  if (isEdgeRuntime(params.pageRuntime)) {\n    params.onClient()\n    params.onEdgeServer()\n    return\n  }\n\n  params.onClient()\n  params.onServer()\n  return\n}\n\nexport async function createEntrypoints(\n  params: CreateEntrypointsParams\n): Promise<{\n  client: webpack.EntryObject\n  server: webpack.EntryObject\n  edgeServer: webpack.EntryObject\n  middlewareMatchers: undefined\n}> {\n  const {\n    config,\n    pages,\n    pagesDir,\n    isDev,\n    rootDir,\n    rootPaths,\n    appDir,\n    appPaths,\n    pageExtensions,\n  } = params\n  const edgeServer: webpack.EntryObject = {}\n  const server: webpack.EntryObject = {}\n  const client: webpack.EntryObject = {}\n  let middlewareMatchers: MiddlewareMatcher[] | undefined = undefined\n\n  let appPathsPerRoute: Record<string, string[]> = {}\n  if (appDir && appPaths) {\n    for (const pathname in appPaths) {\n      const normalizedPath = normalizeAppPath(pathname)\n      const actualPath = appPaths[pathname]\n      if (!appPathsPerRoute[normalizedPath]) {\n        appPathsPerRoute[normalizedPath] = []\n      }\n      appPathsPerRoute[normalizedPath].push(\n        // TODO-APP: refactor to pass the page path from createPagesMapping instead.\n        getPageFromPath(actualPath, pageExtensions).replace(APP_DIR_ALIAS, '')\n      )\n    }\n\n    // TODO: find a better place to do this\n    normalizeCatchAllRoutes(appPathsPerRoute)\n\n    // Make sure to sort parallel routes to make the result deterministic.\n    appPathsPerRoute = Object.fromEntries(\n      Object.entries(appPathsPerRoute).map(([k, v]) => [k, v.sort()])\n    )\n  }\n\n  const getEntryHandler =\n    (mappings: MappedPages, pagesType: PAGE_TYPES): ((page: string) => void) =>\n    async (page) => {\n      const bundleFile = normalizePagePath(page)\n      const clientBundlePath = posix.join(pagesType, bundleFile)\n      const serverBundlePath =\n        pagesType === PAGE_TYPES.PAGES\n          ? posix.join('pages', bundleFile)\n          : pagesType === PAGE_TYPES.APP\n            ? posix.join('app', bundleFile)\n            : bundleFile.slice(1)\n\n      const absolutePagePath = mappings[page]\n\n      // Handle paths that have aliases\n      const pageFilePath = getPageFilePath({\n        absolutePagePath,\n        pagesDir,\n        appDir,\n        rootDir,\n      })\n\n      const isInsideAppDir =\n        !!appDir &&\n        (absolutePagePath.startsWith(APP_DIR_ALIAS) ||\n          absolutePagePath.startsWith(appDir))\n\n      const staticInfo: PageStaticInfo = await getStaticInfoIncludingLayouts({\n        isInsideAppDir,\n        pageExtensions,\n        pageFilePath,\n        appDir,\n        config,\n        isDev,\n        page,\n      })\n\n      // TODO(timneutkens): remove this\n      const isServerComponent =\n        isInsideAppDir && staticInfo.rsc !== RSC_MODULE_TYPES.client\n\n      if (isMiddlewareFile(page)) {\n        middlewareMatchers = staticInfo.middleware?.matchers ?? [\n          { regexp: '.*', originalSource: '/:path*' },\n        ]\n      }\n\n      const isInstrumentation =\n        isInstrumentationHookFile(page) && pagesType === PAGE_TYPES.ROOT\n\n      let pageRuntime = staticInfo?.runtime\n\n      if (\n        isMiddlewareFile(page) &&\n        !config.experimental.nodeMiddleware &&\n        pageRuntime === 'nodejs'\n      ) {\n        Log.warn(\n          'nodejs runtime support for middleware requires experimental.nodeMiddleware be enabled in your next.config'\n        )\n        pageRuntime = 'edge'\n      }\n\n      runDependingOnPageType({\n        page,\n        pageRuntime: staticInfo.runtime,\n        pageType: pagesType,\n        onClient: () => {\n          if (isServerComponent || isInsideAppDir) {\n            // We skip the initial entries for server component pages and let the\n            // server compiler inject them instead.\n          } else {\n            client[clientBundlePath] = getClientEntry({\n              absolutePagePath,\n              page,\n            })\n          }\n        },\n        onServer: () => {\n          if (pagesType === 'app' && appDir) {\n            const matchedAppPaths = appPathsPerRoute[normalizeAppPath(page)]\n            server[serverBundlePath] = getAppEntry({\n              page,\n              name: serverBundlePath,\n              pagePath: absolutePagePath,\n              appDir,\n              appPaths: matchedAppPaths,\n              pageExtensions,\n              basePath: config.basePath,\n              assetPrefix: config.assetPrefix,\n              nextConfigOutput: config.output,\n              preferredRegion: staticInfo.preferredRegion,\n              middlewareConfig: encodeToBase64(staticInfo.middleware || {}),\n              isGlobalNotFoundEnabled: config.experimental.globalNotFound\n                ? true\n                : undefined,\n            })\n          } else if (isInstrumentation) {\n            server[serverBundlePath.replace('src/', '')] =\n              getInstrumentationEntry({\n                absolutePagePath,\n                isEdgeServer: false,\n                isDev: false,\n              })\n          } else if (isMiddlewareFile(page)) {\n            server[serverBundlePath.replace('src/', '')] = getEdgeServerEntry({\n              ...params,\n              rootDir,\n              absolutePagePath: absolutePagePath,\n              bundlePath: clientBundlePath,\n              isDev: false,\n              isServerComponent,\n              page,\n              middleware: staticInfo?.middleware,\n              pagesType,\n              preferredRegion: staticInfo.preferredRegion,\n              middlewareConfig: staticInfo.middleware,\n            })\n          } else if (isAPIRoute(page)) {\n            server[serverBundlePath] = [\n              getRouteLoaderEntry({\n                kind: RouteKind.PAGES_API,\n                page,\n                absolutePagePath,\n                preferredRegion: staticInfo.preferredRegion,\n                middlewareConfig: staticInfo.middleware || {},\n              }),\n            ]\n          } else if (\n            !isMiddlewareFile(page) &&\n            !isInternalComponent(absolutePagePath) &&\n            !isNonRoutePagesPage(page)\n          ) {\n            server[serverBundlePath] = [\n              getRouteLoaderEntry({\n                kind: RouteKind.PAGES,\n                page,\n                pages,\n                absolutePagePath,\n                preferredRegion: staticInfo.preferredRegion,\n                middlewareConfig: staticInfo.middleware ?? {},\n              }),\n            ]\n          } else {\n            server[serverBundlePath] = [absolutePagePath]\n          }\n        },\n        onEdgeServer: () => {\n          let appDirLoader: string = ''\n          if (isInstrumentation) {\n            edgeServer[serverBundlePath.replace('src/', '')] =\n              getInstrumentationEntry({\n                absolutePagePath,\n                isEdgeServer: true,\n                isDev: false,\n              })\n          } else {\n            if (pagesType === 'app') {\n              const matchedAppPaths = appPathsPerRoute[normalizeAppPath(page)]\n              appDirLoader = getAppEntry({\n                name: serverBundlePath,\n                page,\n                pagePath: absolutePagePath,\n                appDir: appDir!,\n                appPaths: matchedAppPaths,\n                pageExtensions,\n                basePath: config.basePath,\n                assetPrefix: config.assetPrefix,\n                nextConfigOutput: config.output,\n                // This isn't used with edge as it needs to be set on the entry module, which will be the `edgeServerEntry` instead.\n                // Still passing it here for consistency.\n                preferredRegion: staticInfo.preferredRegion,\n                middlewareConfig: Buffer.from(\n                  JSON.stringify(staticInfo.middleware || {})\n                ).toString('base64'),\n                isGlobalNotFoundEnabled: config.experimental.globalNotFound\n                  ? true\n                  : undefined,\n              }).import\n            }\n            edgeServer[serverBundlePath] = getEdgeServerEntry({\n              ...params,\n              rootDir,\n              absolutePagePath: absolutePagePath,\n              bundlePath: clientBundlePath,\n              isDev: false,\n              isServerComponent,\n              page,\n              middleware: staticInfo?.middleware,\n              pagesType,\n              appDirLoader,\n              preferredRegion: staticInfo.preferredRegion,\n              middlewareConfig: staticInfo.middleware,\n            })\n          }\n        },\n      })\n    }\n\n  const promises: Promise<void[]>[] = []\n\n  if (appPaths) {\n    const entryHandler = getEntryHandler(appPaths, PAGE_TYPES.APP)\n    promises.push(Promise.all(Object.keys(appPaths).map(entryHandler)))\n  }\n  if (rootPaths) {\n    promises.push(\n      Promise.all(\n        Object.keys(rootPaths).map(getEntryHandler(rootPaths, PAGE_TYPES.ROOT))\n      )\n    )\n  }\n  promises.push(\n    Promise.all(\n      Object.keys(pages).map(getEntryHandler(pages, PAGE_TYPES.PAGES))\n    )\n  )\n\n  await Promise.all(promises)\n\n  // Optimization: If there's only one instrumentation hook in edge compiler, which means there's no edge server entry.\n  // We remove the edge instrumentation entry from edge compiler as it can be pure server side.\n  if (edgeServer.instrumentation && Object.keys(edgeServer).length === 1) {\n    delete edgeServer.instrumentation\n  }\n\n  return {\n    client,\n    server,\n    edgeServer,\n    middlewareMatchers,\n  }\n}\n\nexport function finalizeEntrypoint({\n  name,\n  compilerType,\n  value,\n  isServerComponent,\n  hasAppDir,\n}: {\n  compilerType?: CompilerNameValues\n  name: string\n  value: ObjectValue<webpack.EntryObject>\n  isServerComponent?: boolean\n  hasAppDir?: boolean\n}): ObjectValue<webpack.EntryObject> {\n  const entry =\n    typeof value !== 'object' || Array.isArray(value)\n      ? { import: value }\n      : value\n\n  const isApi = name.startsWith('pages/api/')\n  const isInstrumentation = isInstrumentationHookFilename(name)\n\n  switch (compilerType) {\n    case COMPILER_NAMES.server: {\n      const layer = isApi\n        ? WEBPACK_LAYERS.apiNode\n        : isInstrumentation\n          ? WEBPACK_LAYERS.instrument\n          : isServerComponent\n            ? WEBPACK_LAYERS.reactServerComponents\n            : name.startsWith('pages/')\n              ? WEBPACK_LAYERS.pagesDirNode\n              : undefined\n\n      return {\n        publicPath: isApi ? '' : undefined,\n        runtime: isApi ? 'webpack-api-runtime' : 'webpack-runtime',\n        layer,\n        ...entry,\n      }\n    }\n    case COMPILER_NAMES.edgeServer: {\n      return {\n        layer: isApi\n          ? WEBPACK_LAYERS.apiEdge\n          : isMiddlewareFilename(name) || isInstrumentation\n            ? WEBPACK_LAYERS.middleware\n            : name.startsWith('pages/')\n              ? WEBPACK_LAYERS.pagesDirEdge\n              : undefined,\n        library: { name: ['_ENTRIES', `middleware_[name]`], type: 'assign' },\n        runtime: EDGE_RUNTIME_WEBPACK,\n        asyncChunks: false,\n        ...entry,\n      }\n    }\n    case COMPILER_NAMES.client: {\n      const isAppLayer =\n        hasAppDir &&\n        (name === CLIENT_STATIC_FILES_RUNTIME_MAIN_APP ||\n          name === APP_CLIENT_INTERNALS ||\n          name.startsWith('app/'))\n\n      if (\n        // Client special cases\n        name !== CLIENT_STATIC_FILES_RUNTIME_POLYFILLS &&\n        name !== CLIENT_STATIC_FILES_RUNTIME_MAIN &&\n        name !== CLIENT_STATIC_FILES_RUNTIME_MAIN_APP &&\n        name !== CLIENT_STATIC_FILES_RUNTIME_AMP &&\n        name !== CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH\n      ) {\n        if (isAppLayer) {\n          return {\n            dependOn: CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n            layer: WEBPACK_LAYERS.appPagesBrowser,\n            ...entry,\n          }\n        }\n\n        return {\n          dependOn:\n            name.startsWith('pages/') && name !== 'pages/_app'\n              ? 'pages/_app'\n              : CLIENT_STATIC_FILES_RUNTIME_MAIN,\n          layer: WEBPACK_LAYERS.pagesDirBrowser,\n          ...entry,\n        }\n      }\n\n      if (isAppLayer) {\n        return {\n          layer: WEBPACK_LAYERS.appPagesBrowser,\n          ...entry,\n        }\n      }\n\n      return {\n        layer: WEBPACK_LAYERS.pagesDirBrowser,\n        ...entry,\n      }\n    }\n    default: {\n      // Should never happen.\n      throw new Error('Invalid compiler type')\n    }\n  }\n}\n"], "names": ["createEntrypoints", "createPagesMapping", "finalizeEntrypoint", "getAppEntry", "getApp<PERSON><PERSON>der", "getClientEntry", "getEdgeServerEntry", "getInstrumentationEntry", "getPageFilePath", "getPageFromPath", "getStaticInfoIncludingLayouts", "runDependingOnPageType", "sortByPageExts", "pageExtensions", "a", "b", "aExt", "extname", "bExt", "aNoExt", "substring", "length", "bNoExt", "aExtIndex", "indexOf", "bExtIndex", "isInsideAppDir", "pageFilePath", "appDir", "config", "nextConfig", "isDev", "page", "pageType", "PAGE_TYPES", "APP", "PAGES", "pageStaticInfo", "getPageStaticInfo", "type", "segments", "isAppPageRoute", "layoutFiles", "potentialLayoutFiles", "map", "ext", "dir", "dirname", "startsWith", "potentialLayoutFile", "layoutFile", "join", "fs", "existsSync", "push", "layoutStaticInfo", "getAppPageStaticInfo", "unshift", "reduceAppConfig", "runtime", "preferredRegion", "maxDuration", "pagePath", "normalizePathSep", "replace", "RegExp", "absolutePagePath", "pagesDir", "rootDir", "PAGES_DIR_ALIAS", "APP_DIR_ALIAS", "ROOT_DIR_ALIAS", "require", "resolve", "pagePaths", "pagesType", "isAppRoute", "pages", "promises", "endsWith", "includes", "page<PERSON><PERSON>", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "normalizedPath", "route", "normalizeMetadataRoute", "isMetadataRouteFile", "filePath", "staticInfo", "normalizeMetadataPageToRoute", "generateImageMetadata", "generateSitemaps", "Promise", "all", "ROOT", "hasAppPages", "Object", "keys", "some", "root", "opts", "isAppRouteRoute", "appDirLoader", "loaderParams", "<PERSON><PERSON><PERSON>", "from", "toString", "JSON", "stringify", "middlewareConfig", "cacheHandlers", "experimental", "import", "layer", "WEBPACK_LAYERS", "reactServerComponents", "isMiddlewareFile", "matchers", "middleware", "encodeMatchers", "isAPIRoute", "apiEdge", "absolute500Path", "absoluteAppPath", "absoluteDocumentPath", "absoluteErrorPath", "dev", "isServerComponent", "stringifiedConfig", "sriEnabled", "sri", "algorithm", "cache<PERSON><PERSON><PERSON>", "serverActions", "serverSideRendering", "undefined", "filename", "isEdgeServer", "INSTRUMENTATION_HOOK_FILENAME", "instrument", "process", "env", "BUILTIN_APP_LOADER", "NEXT_RSPACK", "projectRoot", "normalize", "__dirname", "loaderOptions", "page<PERSON><PERSON>der", "params", "isInstrumentationHookFile", "onServer", "onEdgeServer", "pageRuntime", "isEdgeRuntime", "onClient", "rootPaths", "appPaths", "edgeServer", "server", "client", "middlewareMatchers", "appPathsPerRoute", "pathname", "normalizeAppPath", "actualPath", "normalizeCatchAllRoutes", "fromEntries", "entries", "k", "v", "sort", "getEntryHandler", "mappings", "bundleFile", "normalizePagePath", "clientBundlePath", "posix", "serverBundlePath", "slice", "rsc", "RSC_MODULE_TYPES", "regexp", "originalSource", "isInstrumentation", "nodeMiddleware", "Log", "warn", "matchedAppPaths", "name", "basePath", "assetPrefix", "nextConfigOutput", "output", "encodeToBase64", "isGlobalNotFoundEnabled", "globalNotFound", "bundlePath", "getRouteLoaderEntry", "kind", "RouteKind", "PAGES_API", "isInternalComponent", "isNonRoutePagesPage", "<PERSON><PERSON><PERSON><PERSON>", "instrumentation", "compilerType", "value", "hasAppDir", "entry", "Array", "isArray", "isApi", "isInstrumentationHookFilename", "COMPILER_NAMES", "apiNode", "pagesDirNode", "publicPath", "isMiddlewareFilename", "pagesDirEdge", "library", "EDGE_RUNTIME_WEBPACK", "asyncChunks", "isApp<PERSON><PERSON>er", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "APP_CLIENT_INTERNALS", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_AMP", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "dependOn", "appPagesBrowser", "pagesDirBrowser", "Error"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;IAmkBsBA,iBAAiB;eAAjBA;;IAjWAC,kBAAkB;eAAlBA;;IA0nBNC,kBAAkB;eAAlBA;;IAnXAC,WAAW;eAAXA;;IANAC,YAAY;eAAZA;;IAgBAC,cAAc;eAAdA;;IAjJAC,kBAAkB;eAAlBA;;IAgHAC,uBAAuB;eAAvBA;;IA9QAC,eAAe;eAAfA;;IAbAC,eAAe;eAAfA;;IAlFMC,6BAA6B;eAA7BA;;IAiaNC,sBAAsB;eAAtBA;;IAvbAC,cAAc;eAAdA;;;6DApEK;sBAIoC;6BAC/B;2DACX;2BAOR;4BACoB;+BACG;4BAKvB;uBAkBA;mCAIA;kCAC0B;mCACC;0BAED;sCACF;iCAEC;kCAIzB;iCAC6B;qCAI7B;iCAC6B;2BACV;wBACK;yCACS;2BAGb;gCACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExB,SAASA,eAAeC,cAA8B;IAC3D,OAAO,CAACC,GAAWC;QACjB,uDAAuD;QACvD,wDAAwD;QACxD,qDAAqD;QACrD,kBAAkB;QAClB,MAAMC,OAAOC,IAAAA,aAAO,EAACH;QACrB,MAAMI,OAAOD,IAAAA,aAAO,EAACF;QAErB,MAAMI,SAASL,EAAEM,SAAS,CAAC,GAAGN,EAAEO,MAAM,GAAGL,KAAKK,MAAM;QACpD,MAAMC,SAASR,EAAEM,SAAS,CAAC,GAAGL,EAAEM,MAAM,GAAGH,KAAKG,MAAM;QAEpD,IAAIF,WAAWG,QAAQ,OAAO;QAE9B,oEAAoE;QACpE,MAAMC,YAAYV,eAAeW,OAAO,CAACR,KAAKI,SAAS,CAAC;QACxD,MAAMK,YAAYZ,eAAeW,OAAO,CAACN,KAAKE,SAAS,CAAC;QAExD,OAAOK,YAAYF;IACrB;AACF;AAEO,eAAeb,8BAA8B,EAClDgB,cAAc,EACdb,cAAc,EACdc,YAAY,EACZC,MAAM,EACNC,QAAQC,UAAU,EAClBC,KAAK,EACLC,IAAI,EASL;IACC,6EAA6E;IAC7E,MAAMC,WAAWP,iBAAiBQ,qBAAU,CAACC,GAAG,GAAGD,qBAAU,CAACE,KAAK;IAEnE,MAAMC,iBAAiB,MAAMC,IAAAA,oCAAiB,EAAC;QAC7CR;QACAH;QACAI;QACAC;QACAC;IACF;IAEA,IAAII,eAAeE,IAAI,KAAKL,qBAAU,CAACE,KAAK,IAAI,CAACR,QAAQ;QACvD,OAAOS;IACT;IAEA,MAAMG,WAAW;QAACH;KAAe;IAEjC,sDAAsD;IACtD,IAAII,IAAAA,8BAAc,EAACT,OAAO;QACxB,MAAMU,cAAc,EAAE;QACtB,MAAMC,uBAAuB9B,eAAe+B,GAAG,CAAC,CAACC,MAAQ,YAAYA;QACrE,IAAIC,MAAMC,IAAAA,aAAO,EAACpB;QAElB,yDAAyD;QACzD,MAAOmB,IAAIE,UAAU,CAACpB,QAAS;YAC7B,KAAK,MAAMqB,uBAAuBN,qBAAsB;gBACtD,MAAMO,aAAaC,IAAAA,UAAI,EAACL,KAAKG;gBAC7B,IAAI,CAACG,WAAE,CAACC,UAAU,CAACH,aAAa;oBAC9B;gBACF;gBACAR,YAAYY,IAAI,CAACJ;YACnB;YACA,6BAA6B;YAC7BJ,MAAMK,IAAAA,UAAI,EAACL,KAAK;QAClB;QAEA,KAAK,MAAMI,cAAcR,YAAa;YACpC,MAAMa,mBAAmB,MAAMC,IAAAA,uCAAoB,EAAC;gBAClD1B;gBACAH,cAAcuB;gBACdnB;gBACAC;gBACAC,UAAUP,iBAAiBQ,qBAAU,CAACC,GAAG,GAAGD,qBAAU,CAACE,KAAK;YAC9D;YAEAI,SAASiB,OAAO,CAACF;QACnB;IACF;IAEA,MAAM1B,SAAS6B,IAAAA,sBAAe,EAAClB;IAE/B,OAAO;QACL,GAAGH,cAAc;QACjBR;QACA8B,SAAS9B,OAAO8B,OAAO;QACvBC,iBAAiB/B,OAAO+B,eAAe;QACvCC,aAAahC,OAAOgC,WAAW;IACjC;AACF;AAOO,SAASpD,gBACdqD,QAAgB,EAChBjD,cAA8B;IAE9B,IAAImB,OAAO+B,IAAAA,kCAAgB,EACzBD,SAASE,OAAO,CAAC,IAAIC,OAAO,CAAC,KAAK,EAAEpD,eAAesC,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG;IAGrEnB,OAAOA,KAAKgC,OAAO,CAAC,YAAY;IAEhC,OAAOhC,SAAS,KAAK,MAAMA;AAC7B;AAEO,SAASxB,gBAAgB,EAC9B0D,gBAAgB,EAChBC,QAAQ,EACRvC,MAAM,EACNwC,OAAO,EAMR;IACC,IAAIF,iBAAiBlB,UAAU,CAACqB,0BAAe,KAAKF,UAAU;QAC5D,OAAOD,iBAAiBF,OAAO,CAACK,0BAAe,EAAEF;IACnD;IAEA,IAAID,iBAAiBlB,UAAU,CAACsB,wBAAa,KAAK1C,QAAQ;QACxD,OAAOsC,iBAAiBF,OAAO,CAACM,wBAAa,EAAE1C;IACjD;IAEA,IAAIsC,iBAAiBlB,UAAU,CAACuB,yBAAc,GAAG;QAC/C,OAAOL,iBAAiBF,OAAO,CAACO,yBAAc,EAAEH;IAClD;IAEA,OAAOI,QAAQC,OAAO,CAACP;AACzB;AAMO,eAAejE,mBAAmB,EACvC8B,KAAK,EACLlB,cAAc,EACd6D,SAAS,EACTC,SAAS,EACTR,QAAQ,EACRvC,MAAM,EAQP;IACC,MAAMgD,aAAaD,cAAc;IACjC,MAAME,QAAqB,CAAC;IAC5B,MAAMC,WAAWJ,UAAU9B,GAAG,CAAgB,OAAOkB;QACnD,uCAAuC;QACvC,IAAIA,SAASiB,QAAQ,CAAC,YAAYlE,eAAemE,QAAQ,CAAC,OAAO;YAC/D;QACF;QAEA,IAAIC,UAAUxE,gBAAgBqD,UAAUjD;QACxC,IAAI+D,YAAY;YACdK,UAAUA,QAAQjB,OAAO,CAAC,QAAQ;YAClC,IAAIiB,YAAY,cAAc;gBAC5BA,UAAUC,4CAAgC;YAC5C;QACF;QAEA,MAAMC,iBAAiBpB,IAAAA,kCAAgB,EACrCZ,IAAAA,UAAI,EACFwB,cAAc,UACVN,0BAAe,GACfM,cAAc,QACZL,wBAAa,GACbC,yBAAc,EACpBT;QAIJ,IAAIsB,QAAQT,cAAc,QAAQU,IAAAA,wCAAsB,EAACJ,WAAWA;QAEpE,IACEN,cAAc,SACdW,IAAAA,oCAAmB,EAACxB,UAAUjD,gBAAgB,OAC9C;YACA,MAAM0E,WAAWpC,IAAAA,UAAI,EAACvB,QAASkC;YAC/B,MAAM0B,aAAa,MAAMlD,IAAAA,oCAAiB,EAAC;gBACzCR,YAAY,CAAC;gBACbH,cAAc4D;gBACdxD;gBACAC,MAAMiD;gBACNhD,UAAU0C;YACZ;YAEAS,QAAQK,IAAAA,8CAA4B,EAClCL,OACA,CAAC,CAAEI,CAAAA,WAAWE,qBAAqB,IAAIF,WAAWG,gBAAgB,AAAD;QAErE;QAEAd,KAAK,CAACO,MAAM,GAAGD;IACjB;IAEA,MAAMS,QAAQC,GAAG,CAACf;IAElB,OAAQH;QACN,KAAKzC,qBAAU,CAAC4D,IAAI;YAAE;gBACpB,OAAOjB;YACT;QACA,KAAK3C,qBAAU,CAACC,GAAG;YAAE;gBACnB,MAAM4D,cAAcC,OAAOC,IAAI,CAACpB,OAAOqB,IAAI,CAAC,CAAClE,OAC3CA,KAAK+C,QAAQ,CAAC;gBAEhB,OAAO;oBACL,4EAA4E;oBAC5E,4EAA4E;oBAC5E,GAAIgB,eAAe;wBACjB,CAACb,4CAAgC,CAAC,EAAEV,QAAQC,OAAO,CACjD;oBAEJ,CAAC;oBACD,GAAGI,KAAK;gBACV;YACF;QACA,KAAK3C,qBAAU,CAACE,KAAK;YAAE;gBACrB,IAAIL,OAAO;oBACT,OAAO8C,KAAK,CAAC,QAAQ;oBACrB,OAAOA,KAAK,CAAC,UAAU;oBACvB,OAAOA,KAAK,CAAC,aAAa;gBAC5B;gBAEA,uEAAuE;gBACvE,uEAAuE;gBACvE,oBAAoB;gBACpB,MAAMsB,OAAOpE,SAASoC,WAAWE,0BAAe,GAAG;gBAEnD,OAAO;oBACL,SAAS,GAAG8B,KAAK,KAAK,CAAC;oBACvB,WAAW,GAAGA,KAAK,OAAO,CAAC;oBAC3B,cAAc,GAAGA,KAAK,UAAU,CAAC;oBACjC,GAAGtB,KAAK;gBACV;YACF;QACA;YAAS;gBACP,OAAO,CAAC;YACV;IACF;AACF;AAkBO,SAASvE,mBAAmB8F,IAgBlC;QA6EgCA;IA5E/B,IACEA,KAAKzB,SAAS,KAAK,SACnB0B,IAAAA,gCAAe,EAACD,KAAKpE,IAAI,KACzBoE,KAAKE,YAAY,EACjB;QACA,MAAMC,eAAwC;YAC5CrC,kBAAkBkC,KAAKlC,gBAAgB;YACvClC,MAAMoE,KAAKpE,IAAI;YACfsE,cAAcE,OAAOC,IAAI,CAACL,KAAKE,YAAY,IAAI,IAAII,QAAQ,CAAC;YAC5D5E,YAAY0E,OAAOC,IAAI,CAACE,KAAKC,SAAS,CAACR,KAAKvE,MAAM,GAAG6E,QAAQ,CAAC;YAC9D9C,iBAAiBwC,KAAKxC,eAAe;YACrCiD,kBAAkBL,OAAOC,IAAI,CAC3BE,KAAKC,SAAS,CAACR,KAAKS,gBAAgB,IAAI,CAAC,IACzCH,QAAQ,CAAC;YACXI,eAAeH,KAAKC,SAAS,CAC3BR,KAAKvE,MAAM,CAACkF,YAAY,CAACD,aAAa,IAAI,CAAC;QAE/C;QAEA,OAAO;YACLE,QAAQ,CAAC,2BAA2B,EAAEJ,IAAAA,sBAAS,EAACL,cAAc,CAAC,CAAC;YAChEU,OAAOC,yBAAc,CAACC,qBAAqB;QAC7C;IACF;IAEA,IAAIC,IAAAA,uBAAgB,EAAChB,KAAKpE,IAAI,GAAG;YAKnBoE;QAJZ,MAAMG,eAAwC;YAC5CrC,kBAAkBkC,KAAKlC,gBAAgB;YACvClC,MAAMoE,KAAKpE,IAAI;YACfoC,SAASgC,KAAKhC,OAAO;YACrBiD,UAAUjB,EAAAA,mBAAAA,KAAKkB,UAAU,qBAAflB,iBAAiBiB,QAAQ,IAC/BE,IAAAA,oCAAc,EAACnB,KAAKkB,UAAU,CAACD,QAAQ,IACvC;YACJzD,iBAAiBwC,KAAKxC,eAAe;YACrCiD,kBAAkBL,OAAOC,IAAI,CAC3BE,KAAKC,SAAS,CAACR,KAAKS,gBAAgB,IAAI,CAAC,IACzCH,QAAQ,CAAC;QACb;QAEA,OAAO;YACLM,QAAQ,CAAC,uBAAuB,EAAEJ,IAAAA,sBAAS,EAACL,cAAc,CAAC,CAAC;YAC5DU,OAAOC,yBAAc,CAACI,UAAU;QAClC;IACF;IAEA,IAAIE,IAAAA,sBAAU,EAACpB,KAAKpE,IAAI,GAAG;QACzB,MAAMuE,eAA0C;YAC9CrC,kBAAkBkC,KAAKlC,gBAAgB;YACvClC,MAAMoE,KAAKpE,IAAI;YACfoC,SAASgC,KAAKhC,OAAO;YACrBR,iBAAiBwC,KAAKxC,eAAe;YACrCiD,kBAAkBL,OAAOC,IAAI,CAC3BE,KAAKC,SAAS,CAACR,KAAKS,gBAAgB,IAAI,CAAC,IACzCH,QAAQ,CAAC;QACb;QAEA,OAAO;YACLM,QAAQ,CAAC,0BAA0B,EAAEJ,IAAAA,sBAAS,EAACL,cAAc,CAAC,CAAC;YAC/DU,OAAOC,yBAAc,CAACO,OAAO;QAC/B;IACF;IAEA,MAAMlB,eAAmC;QACvCmB,iBAAiBtB,KAAKvB,KAAK,CAAC,OAAO,IAAI;QACvC8C,iBAAiBvB,KAAKvB,KAAK,CAAC,QAAQ;QACpC+C,sBAAsBxB,KAAKvB,KAAK,CAAC,aAAa;QAC9CgD,mBAAmBzB,KAAKvB,KAAK,CAAC,UAAU;QACxCX,kBAAkBkC,KAAKlC,gBAAgB;QACvC4D,KAAK1B,KAAKrE,KAAK;QACfgG,mBAAmB3B,KAAK2B,iBAAiB;QACzC/F,MAAMoE,KAAKpE,IAAI;QACfgG,mBAAmBxB,OAAOC,IAAI,CAACE,KAAKC,SAAS,CAACR,KAAKvE,MAAM,GAAG6E,QAAQ,CAClE;QAEF/B,WAAWyB,KAAKzB,SAAS;QACzB2B,cAAcE,OAAOC,IAAI,CAACL,KAAKE,YAAY,IAAI,IAAII,QAAQ,CAAC;QAC5DuB,YAAY,CAAC7B,KAAKrE,KAAK,IAAI,CAAC,GAACqE,gCAAAA,KAAKvE,MAAM,CAACkF,YAAY,CAACmB,GAAG,qBAA5B9B,8BAA8B+B,SAAS;QACpEC,cAAchC,KAAKvE,MAAM,CAACuG,YAAY;QACtCxE,iBAAiBwC,KAAKxC,eAAe;QACrCiD,kBAAkBL,OAAOC,IAAI,CAC3BE,KAAKC,SAAS,CAACR,KAAKS,gBAAgB,IAAI,CAAC,IACzCH,QAAQ,CAAC;QACX2B,eAAejC,KAAKvE,MAAM,CAACkF,YAAY,CAACsB,aAAa;QACrDvB,eAAeH,KAAKC,SAAS,CAACR,KAAKvE,MAAM,CAACkF,YAAY,CAACD,aAAa,IAAI,CAAC;IAC3E;IAEA,OAAO;QACLE,QAAQ,CAAC,qBAAqB,EAAEL,KAAKC,SAAS,CAACL,cAAc,CAAC,CAAC;QAC/D,sEAAsE;QACtE,2EAA2E;QAC3E,sBAAsB;QACtBU,OAAOb,KAAKE,YAAY,GAAGY,yBAAc,CAACoB,mBAAmB,GAAGC;IAClE;AACF;AAEO,SAAShI,wBAAwB6F,IAIvC;IACC,2DAA2D;IAC3D,MAAMoC,WAAW,GACfpC,KAAKqC,YAAY,GAAG,UAAUrC,KAAKrE,KAAK,GAAG,KAAK,QAC/C2G,wCAA6B,CAAC,GAAG,CAAC;IAErC,OAAO;QACL1B,QAAQZ,KAAKlC,gBAAgB;QAC7BsE;QACAvB,OAAOC,yBAAc,CAACyB,UAAU;IAClC;AACF;AAEO,SAASvI;IACd,OAAOwI,QAAQC,GAAG,CAACC,kBAAkB,GACjC,CAAC,uBAAuB,CAAC,GACzB;AACN;AAEO,SAAS3I,YAAYiG,IAAgC;IAC1D,IAAIwC,QAAQC,GAAG,CAACE,WAAW,IAAIH,QAAQC,GAAG,CAACC,kBAAkB,EAAE;;QAC3D1C,KAAa4C,WAAW,GAAGC,IAAAA,eAAS,EAAC9F,IAAAA,UAAI,EAAC+F,WAAW;IACzD;IACA,OAAO;QACLlC,QAAQ,GAAG5G,eAAe,CAAC,EAAEwG,IAAAA,sBAAS,EAACR,MAAM,CAAC,CAAC;QAC/Ca,OAAOC,yBAAc,CAACC,qBAAqB;IAC7C;AACF;AAEO,SAAS9G,eAAe+F,IAG9B;IACC,MAAM+C,gBAA0C;QAC9CjF,kBAAkBkC,KAAKlC,gBAAgB;QACvClC,MAAMoE,KAAKpE,IAAI;IACjB;IAEA,MAAMoH,aAAa,CAAC,yBAAyB,EAAExC,IAAAA,sBAAS,EAACuC,eAAe,CAAC,CAAC;IAE1E,wEAAwE;IACxE,kEAAkE;IAClE,UAAU;IACV,OAAO/C,KAAKpE,IAAI,KAAK,UACjB;QAACoH;QAAY5E,QAAQC,OAAO,CAAC;KAAoB,GACjD2E;AACN;AAEO,SAASzI,uBAA0B0I,MAOzC;IACC,IACEA,OAAOpH,QAAQ,KAAKC,qBAAU,CAAC4D,IAAI,IACnCwD,IAAAA,gCAAyB,EAACD,OAAOrH,IAAI,GACrC;QACAqH,OAAOE,QAAQ;QACfF,OAAOG,YAAY;QACnB;IACF;IAEA,IAAIpC,IAAAA,uBAAgB,EAACiC,OAAOrH,IAAI,GAAG;QACjC,IAAIqH,OAAOI,WAAW,KAAK,UAAU;YACnCJ,OAAOE,QAAQ;YACf;QACF,OAAO;YACLF,OAAOG,YAAY;YACnB;QACF;IACF;IAEA,IAAIhC,IAAAA,sBAAU,EAAC6B,OAAOrH,IAAI,GAAG;QAC3B,IAAI0H,IAAAA,4BAAa,EAACL,OAAOI,WAAW,GAAG;YACrCJ,OAAOG,YAAY;YACnB;QACF;QAEAH,OAAOE,QAAQ;QACf;IACF;IACA,IAAIF,OAAOrH,IAAI,KAAK,cAAc;QAChCqH,OAAOE,QAAQ;QACf;IACF;IACA,IACEF,OAAOrH,IAAI,KAAK,WAChBqH,OAAOrH,IAAI,KAAK,aAChBqH,OAAOrH,IAAI,KAAK,UAChBqH,OAAOrH,IAAI,KAAK,QAChB;QACAqH,OAAOM,QAAQ;QACfN,OAAOE,QAAQ;QACf;IACF;IACA,IAAIG,IAAAA,4BAAa,EAACL,OAAOI,WAAW,GAAG;QACrCJ,OAAOM,QAAQ;QACfN,OAAOG,YAAY;QACnB;IACF;IAEAH,OAAOM,QAAQ;IACfN,OAAOE,QAAQ;IACf;AACF;AAEO,eAAevJ,kBACpBqJ,MAA+B;IAO/B,MAAM,EACJxH,MAAM,EACNgD,KAAK,EACLV,QAAQ,EACRpC,KAAK,EACLqC,OAAO,EACPwF,SAAS,EACThI,MAAM,EACNiI,QAAQ,EACRhJ,cAAc,EACf,GAAGwI;IACJ,MAAMS,aAAkC,CAAC;IACzC,MAAMC,SAA8B,CAAC;IACrC,MAAMC,SAA8B,CAAC;IACrC,IAAIC,qBAAsD1B;IAE1D,IAAI2B,mBAA6C,CAAC;IAClD,IAAItI,UAAUiI,UAAU;QACtB,IAAK,MAAMM,YAAYN,SAAU;YAC/B,MAAM1E,iBAAiBiF,IAAAA,0BAAgB,EAACD;YACxC,MAAME,aAAaR,QAAQ,CAACM,SAAS;YACrC,IAAI,CAACD,gBAAgB,CAAC/E,eAAe,EAAE;gBACrC+E,gBAAgB,CAAC/E,eAAe,GAAG,EAAE;YACvC;YACA+E,gBAAgB,CAAC/E,eAAe,CAAC7B,IAAI,CACnC,4EAA4E;YAC5E7C,gBAAgB4J,YAAYxJ,gBAAgBmD,OAAO,CAACM,wBAAa,EAAE;QAEvE;QAEA,uCAAuC;QACvCgG,IAAAA,gDAAuB,EAACJ;QAExB,sEAAsE;QACtEA,mBAAmBlE,OAAOuE,WAAW,CACnCvE,OAAOwE,OAAO,CAACN,kBAAkBtH,GAAG,CAAC,CAAC,CAAC6H,GAAGC,EAAE,GAAK;gBAACD;gBAAGC,EAAEC,IAAI;aAAG;IAElE;IAEA,MAAMC,kBACJ,CAACC,UAAuBlG,YACxB,OAAO3C;YACL,MAAM8I,aAAaC,IAAAA,oCAAiB,EAAC/I;YACrC,MAAMgJ,mBAAmBC,WAAK,CAAC9H,IAAI,CAACwB,WAAWmG;YAC/C,MAAMI,mBACJvG,cAAczC,qBAAU,CAACE,KAAK,GAC1B6I,WAAK,CAAC9H,IAAI,CAAC,SAAS2H,cACpBnG,cAAczC,qBAAU,CAACC,GAAG,GAC1B8I,WAAK,CAAC9H,IAAI,CAAC,OAAO2H,cAClBA,WAAWK,KAAK,CAAC;YAEzB,MAAMjH,mBAAmB2G,QAAQ,CAAC7I,KAAK;YAEvC,iCAAiC;YACjC,MAAML,eAAenB,gBAAgB;gBACnC0D;gBACAC;gBACAvC;gBACAwC;YACF;YAEA,MAAM1C,iBACJ,CAAC,CAACE,UACDsC,CAAAA,iBAAiBlB,UAAU,CAACsB,wBAAa,KACxCJ,iBAAiBlB,UAAU,CAACpB,OAAM;YAEtC,MAAM4D,aAA6B,MAAM9E,8BAA8B;gBACrEgB;gBACAb;gBACAc;gBACAC;gBACAC;gBACAE;gBACAC;YACF;YAEA,iCAAiC;YACjC,MAAM+F,oBACJrG,kBAAkB8D,WAAW4F,GAAG,KAAKC,4BAAgB,CAACrB,MAAM;YAE9D,IAAI5C,IAAAA,uBAAgB,EAACpF,OAAO;oBACLwD;gBAArByE,qBAAqBzE,EAAAA,yBAAAA,WAAW8B,UAAU,qBAArB9B,uBAAuB6B,QAAQ,KAAI;oBACtD;wBAAEiE,QAAQ;wBAAMC,gBAAgB;oBAAU;iBAC3C;YACH;YAEA,MAAMC,oBACJlC,IAAAA,gCAAyB,EAACtH,SAAS2C,cAAczC,qBAAU,CAAC4D,IAAI;YAElE,IAAI2D,cAAcjE,8BAAAA,WAAY7B,OAAO;YAErC,IACEyD,IAAAA,uBAAgB,EAACpF,SACjB,CAACH,OAAOkF,YAAY,CAAC0E,cAAc,IACnChC,gBAAgB,UAChB;gBACAiC,KAAIC,IAAI,CACN;gBAEFlC,cAAc;YAChB;YAEA9I,uBAAuB;gBACrBqB;gBACAyH,aAAajE,WAAW7B,OAAO;gBAC/B1B,UAAU0C;gBACVgF,UAAU;oBACR,IAAI5B,qBAAqBrG,gBAAgB;oBACvC,qEAAqE;oBACrE,uCAAuC;oBACzC,OAAO;wBACLsI,MAAM,CAACgB,iBAAiB,GAAG3K,eAAe;4BACxC6D;4BACAlC;wBACF;oBACF;gBACF;gBACAuH,UAAU;oBACR,IAAI5E,cAAc,SAAS/C,QAAQ;wBACjC,MAAMgK,kBAAkB1B,gBAAgB,CAACE,IAAAA,0BAAgB,EAACpI,MAAM;wBAChE+H,MAAM,CAACmB,iBAAiB,GAAG/K,YAAY;4BACrC6B;4BACA6J,MAAMX;4BACNpH,UAAUI;4BACVtC;4BACAiI,UAAU+B;4BACV/K;4BACAiL,UAAUjK,OAAOiK,QAAQ;4BACzBC,aAAalK,OAAOkK,WAAW;4BAC/BC,kBAAkBnK,OAAOoK,MAAM;4BAC/BrI,iBAAiB4B,WAAW5B,eAAe;4BAC3CiD,kBAAkBqF,IAAAA,sBAAc,EAAC1G,WAAW8B,UAAU,IAAI,CAAC;4BAC3D6E,yBAAyBtK,OAAOkF,YAAY,CAACqF,cAAc,GACvD,OACA7D;wBACN;oBACF,OAAO,IAAIiD,mBAAmB;wBAC5BzB,MAAM,CAACmB,iBAAiBlH,OAAO,CAAC,QAAQ,IAAI,GAC1CzD,wBAAwB;4BACtB2D;4BACAuE,cAAc;4BACd1G,OAAO;wBACT;oBACJ,OAAO,IAAIqF,IAAAA,uBAAgB,EAACpF,OAAO;wBACjC+H,MAAM,CAACmB,iBAAiBlH,OAAO,CAAC,QAAQ,IAAI,GAAG1D,mBAAmB;4BAChE,GAAG+I,MAAM;4BACTjF;4BACAF,kBAAkBA;4BAClBmI,YAAYrB;4BACZjJ,OAAO;4BACPgG;4BACA/F;4BACAsF,UAAU,EAAE9B,8BAAAA,WAAY8B,UAAU;4BAClC3C;4BACAf,iBAAiB4B,WAAW5B,eAAe;4BAC3CiD,kBAAkBrB,WAAW8B,UAAU;wBACzC;oBACF,OAAO,IAAIE,IAAAA,sBAAU,EAACxF,OAAO;wBAC3B+H,MAAM,CAACmB,iBAAiB,GAAG;4BACzBoB,IAAAA,oCAAmB,EAAC;gCAClBC,MAAMC,oBAAS,CAACC,SAAS;gCACzBzK;gCACAkC;gCACAN,iBAAiB4B,WAAW5B,eAAe;gCAC3CiD,kBAAkBrB,WAAW8B,UAAU,IAAI,CAAC;4BAC9C;yBACD;oBACH,OAAO,IACL,CAACF,IAAAA,uBAAgB,EAACpF,SAClB,CAAC0K,IAAAA,wCAAmB,EAACxI,qBACrB,CAACyI,IAAAA,wCAAmB,EAAC3K,OACrB;wBACA+H,MAAM,CAACmB,iBAAiB,GAAG;4BACzBoB,IAAAA,oCAAmB,EAAC;gCAClBC,MAAMC,oBAAS,CAACpK,KAAK;gCACrBJ;gCACA6C;gCACAX;gCACAN,iBAAiB4B,WAAW5B,eAAe;gCAC3CiD,kBAAkBrB,WAAW8B,UAAU,IAAI,CAAC;4BAC9C;yBACD;oBACH,OAAO;wBACLyC,MAAM,CAACmB,iBAAiB,GAAG;4BAAChH;yBAAiB;oBAC/C;gBACF;gBACAsF,cAAc;oBACZ,IAAIlD,eAAuB;oBAC3B,IAAIkF,mBAAmB;wBACrB1B,UAAU,CAACoB,iBAAiBlH,OAAO,CAAC,QAAQ,IAAI,GAC9CzD,wBAAwB;4BACtB2D;4BACAuE,cAAc;4BACd1G,OAAO;wBACT;oBACJ,OAAO;wBACL,IAAI4C,cAAc,OAAO;4BACvB,MAAMiH,kBAAkB1B,gBAAgB,CAACE,IAAAA,0BAAgB,EAACpI,MAAM;4BAChEsE,eAAenG,YAAY;gCACzB0L,MAAMX;gCACNlJ;gCACA8B,UAAUI;gCACVtC,QAAQA;gCACRiI,UAAU+B;gCACV/K;gCACAiL,UAAUjK,OAAOiK,QAAQ;gCACzBC,aAAalK,OAAOkK,WAAW;gCAC/BC,kBAAkBnK,OAAOoK,MAAM;gCAC/B,oHAAoH;gCACpH,yCAAyC;gCACzCrI,iBAAiB4B,WAAW5B,eAAe;gCAC3CiD,kBAAkBL,OAAOC,IAAI,CAC3BE,KAAKC,SAAS,CAACpB,WAAW8B,UAAU,IAAI,CAAC,IACzCZ,QAAQ,CAAC;gCACXyF,yBAAyBtK,OAAOkF,YAAY,CAACqF,cAAc,GACvD,OACA7D;4BACN,GAAGvB,MAAM;wBACX;wBACA8C,UAAU,CAACoB,iBAAiB,GAAG5K,mBAAmB;4BAChD,GAAG+I,MAAM;4BACTjF;4BACAF,kBAAkBA;4BAClBmI,YAAYrB;4BACZjJ,OAAO;4BACPgG;4BACA/F;4BACAsF,UAAU,EAAE9B,8BAAAA,WAAY8B,UAAU;4BAClC3C;4BACA2B;4BACA1C,iBAAiB4B,WAAW5B,eAAe;4BAC3CiD,kBAAkBrB,WAAW8B,UAAU;wBACzC;oBACF;gBACF;YACF;QACF;IAEF,MAAMxC,WAA8B,EAAE;IAEtC,IAAI+E,UAAU;QACZ,MAAM+C,eAAehC,gBAAgBf,UAAU3H,qBAAU,CAACC,GAAG;QAC7D2C,SAASxB,IAAI,CAACsC,QAAQC,GAAG,CAACG,OAAOC,IAAI,CAAC4D,UAAUjH,GAAG,CAACgK;IACtD;IACA,IAAIhD,WAAW;QACb9E,SAASxB,IAAI,CACXsC,QAAQC,GAAG,CACTG,OAAOC,IAAI,CAAC2D,WAAWhH,GAAG,CAACgI,gBAAgBhB,WAAW1H,qBAAU,CAAC4D,IAAI;IAG3E;IACAhB,SAASxB,IAAI,CACXsC,QAAQC,GAAG,CACTG,OAAOC,IAAI,CAACpB,OAAOjC,GAAG,CAACgI,gBAAgB/F,OAAO3C,qBAAU,CAACE,KAAK;IAIlE,MAAMwD,QAAQC,GAAG,CAACf;IAElB,qHAAqH;IACrH,6FAA6F;IAC7F,IAAIgF,WAAW+C,eAAe,IAAI7G,OAAOC,IAAI,CAAC6D,YAAYzI,MAAM,KAAK,GAAG;QACtE,OAAOyI,WAAW+C,eAAe;IACnC;IAEA,OAAO;QACL7C;QACAD;QACAD;QACAG;IACF;AACF;AAEO,SAAS/J,mBAAmB,EACjC2L,IAAI,EACJiB,YAAY,EACZC,KAAK,EACLhF,iBAAiB,EACjBiF,SAAS,EAOV;IACC,MAAMC,QACJ,OAAOF,UAAU,YAAYG,MAAMC,OAAO,CAACJ,SACvC;QAAE/F,QAAQ+F;IAAM,IAChBA;IAEN,MAAMK,QAAQvB,KAAK7I,UAAU,CAAC;IAC9B,MAAMwI,oBAAoB6B,IAAAA,oCAA6B,EAACxB;IAExD,OAAQiB;QACN,KAAKQ,0BAAc,CAACvD,MAAM;YAAE;gBAC1B,MAAM9C,QAAQmG,QACVlG,yBAAc,CAACqG,OAAO,GACtB/B,oBACEtE,yBAAc,CAACyB,UAAU,GACzBZ,oBACEb,yBAAc,CAACC,qBAAqB,GACpC0E,KAAK7I,UAAU,CAAC,YACdkE,yBAAc,CAACsG,YAAY,GAC3BjF;gBAEV,OAAO;oBACLkF,YAAYL,QAAQ,KAAK7E;oBACzB5E,SAASyJ,QAAQ,wBAAwB;oBACzCnG;oBACA,GAAGgG,KAAK;gBACV;YACF;QACA,KAAKK,0BAAc,CAACxD,UAAU;YAAE;gBAC9B,OAAO;oBACL7C,OAAOmG,QACHlG,yBAAc,CAACO,OAAO,GACtBiG,IAAAA,2BAAoB,EAAC7B,SAASL,oBAC5BtE,yBAAc,CAACI,UAAU,GACzBuE,KAAK7I,UAAU,CAAC,YACdkE,yBAAc,CAACyG,YAAY,GAC3BpF;oBACRqF,SAAS;wBAAE/B,MAAM;4BAAC;4BAAY,CAAC,iBAAiB,CAAC;yBAAC;wBAAEtJ,MAAM;oBAAS;oBACnEoB,SAASkK,gCAAoB;oBAC7BC,aAAa;oBACb,GAAGb,KAAK;gBACV;YACF;QACA,KAAKK,0BAAc,CAACtD,MAAM;YAAE;gBAC1B,MAAM+D,aACJf,aACCnB,CAAAA,SAASmC,gDAAoC,IAC5CnC,SAASoC,gCAAoB,IAC7BpC,KAAK7I,UAAU,CAAC,OAAM;gBAE1B,IACE,uBAAuB;gBACvB6I,SAASqC,iDAAqC,IAC9CrC,SAASsC,4CAAgC,IACzCtC,SAASmC,gDAAoC,IAC7CnC,SAASuC,2CAA+B,IACxCvC,SAASwC,qDAAyC,EAClD;oBACA,IAAIN,YAAY;wBACd,OAAO;4BACLO,UAAUN,gDAAoC;4BAC9C/G,OAAOC,yBAAc,CAACqH,eAAe;4BACrC,GAAGtB,KAAK;wBACV;oBACF;oBAEA,OAAO;wBACLqB,UACEzC,KAAK7I,UAAU,CAAC,aAAa6I,SAAS,eAClC,eACAsC,4CAAgC;wBACtClH,OAAOC,yBAAc,CAACsH,eAAe;wBACrC,GAAGvB,KAAK;oBACV;gBACF;gBAEA,IAAIc,YAAY;oBACd,OAAO;wBACL9G,OAAOC,yBAAc,CAACqH,eAAe;wBACrC,GAAGtB,KAAK;oBACV;gBACF;gBAEA,OAAO;oBACLhG,OAAOC,yBAAc,CAACsH,eAAe;oBACrC,GAAGvB,KAAK;gBACV;YACF;QACA;YAAS;gBACP,uBAAuB;gBACvB,MAAM,qBAAkC,CAAlC,IAAIwB,MAAM,0BAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiC;YACzC;IACF;AACF", "ignoreList": [0]}