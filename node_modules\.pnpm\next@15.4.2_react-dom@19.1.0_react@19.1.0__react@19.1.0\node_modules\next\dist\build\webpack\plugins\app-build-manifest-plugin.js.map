{"version": 3, "sources": ["../../../../src/build/webpack/plugins/app-build-manifest-plugin.ts"], "sourcesContent": ["import { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  APP_BUILD_MANIFEST,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  SYSTEM_ENTRYPOINTS,\n} from '../../../shared/lib/constants'\nimport { getEntrypointFiles } from './build-manifest-plugin'\nimport getAppRouteFromEntrypoint from '../../../server/get-app-route-from-entrypoint'\n\ntype Options = {\n  dev: boolean\n}\n\nexport type AppBuildManifest = {\n  pages: Record<string, string[]>\n}\n\nconst PLUGIN_NAME = 'AppBuildManifestPlugin'\n\nexport class AppBuildManifestPlugin {\n  private readonly dev: boolean\n\n  constructor(options: Options) {\n    this.dev = options.dev\n  }\n\n  public apply(compiler: any) {\n    compiler.hooks.make.tap(PLUGIN_NAME, (compilation: any) => {\n      compilation.hooks.processAssets.tap(\n        {\n          name: PLUGIN_NAME,\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n        },\n        () => this.createAsset(compilation)\n      )\n    })\n  }\n\n  private createAsset(compilation: webpack.Compilation) {\n    const manifest: AppBuildManifest = {\n      pages: {},\n    }\n\n    const mainFiles = new Set(\n      getEntrypointFiles(\n        compilation.entrypoints.get(CLIENT_STATIC_FILES_RUNTIME_MAIN_APP)\n      )\n    )\n\n    for (const entrypoint of compilation.entrypoints.values()) {\n      if (!entrypoint.name) {\n        continue\n      }\n\n      if (SYSTEM_ENTRYPOINTS.has(entrypoint.name)) {\n        continue\n      }\n\n      const pagePath = getAppRouteFromEntrypoint(entrypoint.name)\n      if (!pagePath) {\n        continue\n      }\n\n      const filesForPage = getEntrypointFiles(entrypoint)\n      manifest.pages[pagePath] = [...new Set([...mainFiles, ...filesForPage])]\n    }\n\n    const json = JSON.stringify(manifest, null, 2)\n\n    compilation.emitAsset(\n      APP_BUILD_MANIFEST,\n      new sources.RawSource(json) as unknown as webpack.sources.RawSource\n    )\n  }\n}\n"], "names": ["AppBuildManifestPlugin", "PLUGIN_NAME", "constructor", "options", "dev", "apply", "compiler", "hooks", "make", "tap", "compilation", "processAssets", "name", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "createAsset", "manifest", "pages", "mainFiles", "Set", "getEntrypointFiles", "entrypoints", "get", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "entrypoint", "values", "SYSTEM_ENTRYPOINTS", "has", "pagePath", "getAppRouteFromEntrypoint", "filesForPage", "json", "JSON", "stringify", "emitAsset", "APP_BUILD_MANIFEST", "sources", "RawSource"], "mappings": ";;;;+BAmBaA;;;eAAAA;;;yBAnBoB;2BAK1B;qCAC4B;kFACG;;;;;;AAUtC,MAAMC,cAAc;AAEb,MAAMD;IAGXE,YAAYC,OAAgB,CAAE;QAC5B,IAAI,CAACC,GAAG,GAAGD,QAAQC,GAAG;IACxB;IAEOC,MAAMC,QAAa,EAAE;QAC1BA,SAASC,KAAK,CAACC,IAAI,CAACC,GAAG,CAACR,aAAa,CAACS;YACpCA,YAAYH,KAAK,CAACI,aAAa,CAACF,GAAG,CACjC;gBACEG,MAAMX;gBACNY,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;YAC3D,GACA,IAAM,IAAI,CAACC,WAAW,CAACP;QAE3B;IACF;IAEQO,YAAYP,WAAgC,EAAE;QACpD,MAAMQ,WAA6B;YACjCC,OAAO,CAAC;QACV;QAEA,MAAMC,YAAY,IAAIC,IACpBC,IAAAA,uCAAkB,EAChBZ,YAAYa,WAAW,CAACC,GAAG,CAACC,+CAAoC;QAIpE,KAAK,MAAMC,cAAchB,YAAYa,WAAW,CAACI,MAAM,GAAI;YACzD,IAAI,CAACD,WAAWd,IAAI,EAAE;gBACpB;YACF;YAEA,IAAIgB,6BAAkB,CAACC,GAAG,CAACH,WAAWd,IAAI,GAAG;gBAC3C;YACF;YAEA,MAAMkB,WAAWC,IAAAA,kCAAyB,EAACL,WAAWd,IAAI;YAC1D,IAAI,CAACkB,UAAU;gBACb;YACF;YAEA,MAAME,eAAeV,IAAAA,uCAAkB,EAACI;YACxCR,SAASC,KAAK,CAACW,SAAS,GAAG;mBAAI,IAAIT,IAAI;uBAAID;uBAAcY;iBAAa;aAAE;QAC1E;QAEA,MAAMC,OAAOC,KAAKC,SAAS,CAACjB,UAAU,MAAM;QAE5CR,YAAY0B,SAAS,CACnBC,6BAAkB,EAClB,IAAIC,gBAAO,CAACC,SAAS,CAACN;IAE1B;AACF", "ignoreList": [0]}