{"version": 3, "sources": ["../../../src/build/swc/jest-transformer.ts"], "sourcesContent": ["/*\nCopyright (c) 2021 The swc Project Developers\n\nPermission is hereby granted, free of charge, to any\nperson obtaining a copy of this software and associated\ndocumentation files (the \"Software\"), to deal in the\nSoftware without restriction, including without\nlimitation the rights to use, copy, modify, merge,\npublish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software\nis furnished to do so, subject to the following\nconditions:\n\nThe above copyright notice and this permission notice\nshall be included in all copies or substantial portions\nof the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF\nANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED\nTO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A\nPARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT\nSHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR\nIN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\nDEALINGS IN THE SOFTWARE.\n*/\n\nimport vm from 'vm'\nimport { transformSync } from './index'\nimport { getJestSWCOptions } from './options'\nimport type {\n  TransformerCreator,\n  TransformOptions,\n  SyncTransformer,\n} from '@jest/transform'\nimport type { Config } from '@jest/types'\nimport type { NextConfig, ExperimentalConfig } from '../../server/config-shared'\nimport type { ResolvedBaseUrl } from '../load-jsconfig'\n\ntype TransformerConfig = Config.TransformerConfig[1]\nexport interface JestTransformerConfig extends TransformerConfig {\n  jsConfig: any\n  resolvedBaseUrl?: ResolvedBaseUrl\n  pagesDir?: string\n  serverComponents?: boolean\n  isEsmProject: boolean\n  modularizeImports?: NextConfig['modularizeImports']\n  swcPlugins: ExperimentalConfig['swcPlugins']\n  compilerOptions: NextConfig['compiler']\n}\n\n// Jest use the `vm` [Module API](https://nodejs.org/api/vm.html#vm_class_vm_module) for ESM.\n// see https://github.com/facebook/jest/issues/9430\nconst isSupportEsm = 'Module' in vm\n\nfunction getJestConfig(\n  jestConfig: TransformOptions<JestTransformerConfig>\n): Config.ProjectConfig {\n  return 'config' in jestConfig\n    ? // jest 27\n      jestConfig.config\n    : // jest 26\n      (jestConfig as unknown as Config.ProjectConfig)\n}\n\nfunction isEsm(\n  isEsmProject: boolean,\n  filename: string,\n  jestConfig: Config.ProjectConfig\n): boolean {\n  return (\n    (/\\.jsx?$/.test(filename) && isEsmProject) ||\n    jestConfig.extensionsToTreatAsEsm?.some((ext: any) =>\n      filename.endsWith(ext)\n    )\n  )\n}\n\nconst createTransformer: TransformerCreator<\n  SyncTransformer<JestTransformerConfig>,\n  JestTransformerConfig\n> = (inputOptions) => ({\n  process(src, filename, jestOptions) {\n    const jestConfig = getJestConfig(jestOptions)\n\n    const swcTransformOpts = getJestSWCOptions({\n      isServer:\n        jestConfig.testEnvironment === 'node' ||\n        jestConfig.testEnvironment.includes('jest-environment-node'),\n      filename,\n      jsConfig: inputOptions?.jsConfig,\n      resolvedBaseUrl: inputOptions?.resolvedBaseUrl,\n      pagesDir: inputOptions?.pagesDir,\n      serverComponents: inputOptions?.serverComponents,\n      modularizeImports: inputOptions?.modularizeImports,\n      swcPlugins: inputOptions?.swcPlugins,\n      compilerOptions: inputOptions?.compilerOptions,\n      serverReferenceHashSalt: '',\n      esm:\n        isSupportEsm &&\n        isEsm(Boolean(inputOptions?.isEsmProject), filename, jestConfig),\n    })\n\n    return transformSync(src, { ...swcTransformOpts, filename })\n  },\n})\n\nmodule.exports = { createTransformer }\n"], "names": ["isSupportEsm", "vm", "getJestConfig", "jestConfig", "config", "isEsm", "isEsmProject", "filename", "test", "extensionsToTreatAsEsm", "some", "ext", "endsWith", "createTransformer", "inputOptions", "process", "src", "jestOptions", "swcTransformOpts", "getJestSWCOptions", "isServer", "testEnvironment", "includes", "jsConfig", "resolvedBaseUrl", "pagesDir", "serverComponents", "modularizeImports", "swcPlugins", "compilerOptions", "serverReferenceHashSalt", "esm", "Boolean", "transformSync", "module", "exports"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;;;;2DAEe;uBACe;yBACI;;;;;;AAsBlC,6FAA6F;AAC7F,mDAAmD;AACnD,MAAMA,eAAe,YAAYC,WAAE;AAEnC,SAASC,cACPC,UAAmD;IAEnD,OAAO,YAAYA,aAEfA,WAAWC,MAAM,GAEhBD;AACP;AAEA,SAASE,MACPC,YAAqB,EACrBC,QAAgB,EAChBJ,UAAgC;QAI9BA;IAFF,OACE,AAAC,UAAUK,IAAI,CAACD,aAAaD,kBAC7BH,qCAAAA,WAAWM,sBAAsB,qBAAjCN,mCAAmCO,IAAI,CAAC,CAACC,MACvCJ,SAASK,QAAQ,CAACD;AAGxB;AAEA,MAAME,oBAGF,CAACC,eAAkB,CAAA;QACrBC,SAAQC,GAAG,EAAET,QAAQ,EAAEU,WAAW;YAChC,MAAMd,aAAaD,cAAce;YAEjC,MAAMC,mBAAmBC,IAAAA,0BAAiB,EAAC;gBACzCC,UACEjB,WAAWkB,eAAe,KAAK,UAC/BlB,WAAWkB,eAAe,CAACC,QAAQ,CAAC;gBACtCf;gBACAgB,QAAQ,EAAET,gCAAAA,aAAcS,QAAQ;gBAChCC,eAAe,EAAEV,gCAAAA,aAAcU,eAAe;gBAC9CC,QAAQ,EAAEX,gCAAAA,aAAcW,QAAQ;gBAChCC,gBAAgB,EAAEZ,gCAAAA,aAAcY,gBAAgB;gBAChDC,iBAAiB,EAAEb,gCAAAA,aAAca,iBAAiB;gBAClDC,UAAU,EAAEd,gCAAAA,aAAcc,UAAU;gBACpCC,eAAe,EAAEf,gCAAAA,aAAce,eAAe;gBAC9CC,yBAAyB;gBACzBC,KACE/B,gBACAK,MAAM2B,QAAQlB,gCAAAA,aAAcR,YAAY,GAAGC,UAAUJ;YACzD;YAEA,OAAO8B,IAAAA,oBAAa,EAACjB,KAAK;gBAAE,GAAGE,gBAAgB;gBAAEX;YAAS;QAC5D;IACF,CAAA;AAEA2B,OAAOC,OAAO,GAAG;IAAEtB;AAAkB", "ignoreList": [0]}