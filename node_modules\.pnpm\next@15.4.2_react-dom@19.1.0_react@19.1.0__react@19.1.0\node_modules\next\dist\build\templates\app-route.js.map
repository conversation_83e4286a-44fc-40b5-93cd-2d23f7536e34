{"version": 3, "sources": ["../../../src/build/templates/app-route.ts"], "sourcesContent": ["import {\n  AppRouteRouteModule,\n  type AppRouteRouteHandlerContext,\n  type AppRouteRouteModuleOptions,\n} from '../../server/route-modules/app-route/module.compiled'\nimport { RouteKind } from '../../server/route-kind'\nimport { patchFetch as _patchFetch } from '../../server/lib/patch-fetch'\nimport type { IncomingMessage, ServerResponse } from 'node:http'\nimport { getRequestMeta } from '../../server/request-meta'\nimport { getTracer, type Span, SpanKind } from '../../server/lib/trace/tracer'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { NodeNextRequest, NodeNextResponse } from '../../server/base-http/node'\nimport {\n  NextRequestAdapter,\n  signalFromNodeResponse,\n} from '../../server/web/spec-extension/adapters/next-request'\nimport { BaseServerSpan } from '../../server/lib/trace/constants'\nimport { getRevalidateReason } from '../../server/instrumentation/utils'\nimport { sendResponse } from '../../server/send-response'\nimport {\n  fromNodeOutgoingHttpHeaders,\n  toNodeOutgoingHttpHeaders,\n} from '../../server/web/utils'\nimport { getCacheControlHeader } from '../../server/lib/cache-control'\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from '../../lib/constants'\nimport { NoFallbackError } from '../../shared/lib/no-fallback-error.external'\nimport {\n  CachedRouteKind,\n  type ResponseCacheEntry,\n  type ResponseGenerator,\n} from '../../server/response-cache'\n\nimport * as userland from 'VAR_USERLAND'\n\n// These are injected by the loader afterwards. This is injected as a variable\n// instead of a replacement because this could also be `undefined` instead of\n// an empty string.\ndeclare const nextConfigOutput: AppRouteRouteModuleOptions['nextConfigOutput']\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\n// INJECT:nextConfigOutput\n\nconst routeModule = new AppRouteRouteModule({\n  definition: {\n    kind: RouteKind.APP_ROUTE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    filename: 'VAR_DEFINITION_FILENAME',\n    bundlePath: 'VAR_DEFINITION_BUNDLE_PATH',\n  },\n  distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n  projectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n  resolvedPagePath: 'VAR_RESOLVED_PAGE_PATH',\n  nextConfigOutput,\n  userland,\n})\n\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule\n\nfunction patchFetch() {\n  return _patchFetch({\n    workAsyncStorage,\n    workUnitAsyncStorage,\n  })\n}\n\nexport {\n  routeModule,\n  workAsyncStorage,\n  workUnitAsyncStorage,\n  serverHooks,\n  patchFetch,\n}\n\nexport async function handler(\n  req: IncomingMessage,\n  res: ServerResponse,\n  ctx: {\n    waitUntil: (prom: Promise<void>) => void\n  }\n) {\n  let srcPage = 'VAR_DEFINITION_PAGE'\n\n  // turbopack doesn't normalize `/index` in the page name\n  // so we need to to process dynamic routes properly\n  // TODO: fix turbopack providing differing value from webpack\n  if (process.env.TURBOPACK) {\n    srcPage = srcPage.replace(/\\/index$/, '') || '/'\n  } else if (srcPage === '/index') {\n    // we always normalize /index specifically\n    srcPage = '/'\n  }\n  const multiZoneDraftMode = process.env\n    .__NEXT_MULTI_ZONE_DRAFT_MODE as any as boolean\n\n  const prepareResult = await routeModule.prepare(req, res, {\n    srcPage,\n    multiZoneDraftMode,\n  })\n\n  if (!prepareResult) {\n    res.statusCode = 400\n    res.end('Bad Request')\n    ctx.waitUntil?.(Promise.resolve())\n    return null\n  }\n\n  const {\n    buildId,\n    params,\n    nextConfig,\n    isDraftMode,\n    prerenderManifest,\n    routerServerContext,\n    isOnDemandRevalidate,\n    revalidateOnlyGenerated,\n    resolvedPathname,\n  } = prepareResult\n\n  const normalizedSrcPage = normalizeAppPath(srcPage)\n\n  let isIsr = Boolean(\n    prerenderManifest.dynamicRoutes[normalizedSrcPage] ||\n      prerenderManifest.routes[resolvedPathname]\n  )\n\n  if (isIsr && !isDraftMode) {\n    const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname])\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage]\n\n    if (prerenderInfo) {\n      if (prerenderInfo.fallback === false && !isPrerendered) {\n        throw new NoFallbackError()\n      }\n    }\n  }\n\n  let cacheKey: string | null = null\n\n  if (isIsr && !routeModule.isDev && !isDraftMode) {\n    cacheKey = resolvedPathname\n    // ensure /index and / is normalized to one key\n    cacheKey = cacheKey === '/index' ? '/' : cacheKey\n  }\n\n  const supportsDynamicResponse: boolean =\n    // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true ||\n    // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr\n\n  // This is a revalidation request if the request is for a static\n  // page and it is not being resumed from a postponed render and\n  // it is not a dynamic RSC request then it is a revalidation\n  // request.\n  const isRevalidate = isIsr && !supportsDynamicResponse\n\n  const method = req.method || 'GET'\n  const tracer = getTracer()\n  const activeSpan = tracer.getActiveScopeSpan()\n\n  const context: AppRouteRouteHandlerContext = {\n    params,\n    prerenderManifest,\n    renderOpts: {\n      experimental: {\n        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n      },\n      supportsDynamicResponse,\n      incrementalCache: getRequestMeta(req, 'incrementalCache'),\n      cacheLifeProfiles: nextConfig.experimental?.cacheLife,\n      isRevalidate,\n      waitUntil: ctx.waitUntil,\n      onClose: (cb) => {\n        res.on('close', cb)\n      },\n      onAfterTaskError: undefined,\n      onInstrumentationRequestError: (error, _request, errorContext) =>\n        routeModule.onRequestError(\n          req,\n          error,\n          errorContext,\n          routerServerContext\n        ),\n    },\n    sharedContext: {\n      buildId,\n    },\n  }\n  const nodeNextReq = new NodeNextRequest(req)\n  const nodeNextRes = new NodeNextResponse(res)\n\n  const nextReq = NextRequestAdapter.fromNodeNextRequest(\n    nodeNextReq,\n    signalFromNodeResponse(res)\n  )\n\n  try {\n    const invokeRouteModule = async (span?: Span) => {\n      return routeModule.handle(nextReq, context).finally(() => {\n        if (!span) return\n\n        span.setAttributes({\n          'http.status_code': res.statusCode,\n          'next.rsc': false,\n        })\n\n        const rootSpanAttributes = tracer.getRootSpanAttributes()\n        // We were unable to get attributes, probably OTEL is not enabled\n        if (!rootSpanAttributes) {\n          return\n        }\n\n        if (\n          rootSpanAttributes.get('next.span_type') !==\n          BaseServerSpan.handleRequest\n        ) {\n          console.warn(\n            `Unexpected root span type '${rootSpanAttributes.get(\n              'next.span_type'\n            )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n          )\n          return\n        }\n\n        const route = rootSpanAttributes.get('next.route')\n        if (route) {\n          const name = `${method} ${route}`\n\n          span.setAttributes({\n            'next.route': route,\n            'http.route': route,\n            'next.span_name': name,\n          })\n          span.updateName(name)\n        } else {\n          span.updateName(`${method} ${req.url}`)\n        }\n      })\n    }\n\n    const handleResponse = async (currentSpan?: Span) => {\n      const responseGenerator: ResponseGenerator = async ({\n        previousCacheEntry,\n      }) => {\n        try {\n          if (\n            !getRequestMeta(req, 'minimalMode') &&\n            isOnDemandRevalidate &&\n            revalidateOnlyGenerated &&\n            !previousCacheEntry\n          ) {\n            res.statusCode = 404\n            // on-demand revalidate always sets this header\n            res.setHeader('x-nextjs-cache', 'REVALIDATED')\n            res.end('This page could not be found')\n            return null\n          }\n\n          const response = await invokeRouteModule(currentSpan)\n\n          ;(req as any).fetchMetrics = (context.renderOpts as any).fetchMetrics\n          let pendingWaitUntil = context.renderOpts.pendingWaitUntil\n\n          // Attempt using provided waitUntil if available\n          // if it's not we fallback to sendResponse's handling\n          if (pendingWaitUntil) {\n            if (ctx.waitUntil) {\n              ctx.waitUntil(pendingWaitUntil)\n              pendingWaitUntil = undefined\n            }\n          }\n          const cacheTags = context.renderOpts.collectedTags\n\n          // If the request is for a static response, we can cache it so long\n          // as it's not edge.\n          if (isIsr) {\n            const blob = await response.blob()\n\n            // Copy the headers from the response.\n            const headers = toNodeOutgoingHttpHeaders(response.headers)\n\n            if (cacheTags) {\n              headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n            }\n\n            if (!headers['content-type'] && blob.type) {\n              headers['content-type'] = blob.type\n            }\n\n            const revalidate =\n              typeof context.renderOpts.collectedRevalidate === 'undefined' ||\n              context.renderOpts.collectedRevalidate >= INFINITE_CACHE\n                ? false\n                : context.renderOpts.collectedRevalidate\n\n            const expire =\n              typeof context.renderOpts.collectedExpire === 'undefined' ||\n              context.renderOpts.collectedExpire >= INFINITE_CACHE\n                ? undefined\n                : context.renderOpts.collectedExpire\n\n            // Create the cache entry for the response.\n            const cacheEntry: ResponseCacheEntry = {\n              value: {\n                kind: CachedRouteKind.APP_ROUTE,\n                status: response.status,\n                body: Buffer.from(await blob.arrayBuffer()),\n                headers,\n              },\n              cacheControl: { revalidate, expire },\n            }\n\n            return cacheEntry\n          } else {\n            // send response without caching if not ISR\n            await sendResponse(\n              nodeNextReq,\n              nodeNextRes,\n              response,\n              context.renderOpts.pendingWaitUntil\n            )\n            return null\n          }\n        } catch (err) {\n          // if this is a background revalidate we need to report\n          // the request error here as it won't be bubbled\n          if (previousCacheEntry?.isStale) {\n            await routeModule.onRequestError(\n              req,\n              err,\n              {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                  isRevalidate,\n                  isOnDemandRevalidate,\n                }),\n              },\n              routerServerContext\n            )\n          }\n          throw err\n        }\n      }\n\n      const cacheEntry = await routeModule.handleResponse({\n        req,\n        nextConfig,\n        cacheKey,\n        routeKind: RouteKind.APP_ROUTE,\n        isFallback: false,\n        prerenderManifest,\n        isRoutePPREnabled: false,\n        isOnDemandRevalidate,\n        revalidateOnlyGenerated,\n        responseGenerator,\n        waitUntil: ctx.waitUntil,\n      })\n\n      // we don't create a cacheEntry for ISR\n      if (!isIsr) {\n        return null\n      }\n\n      if (cacheEntry?.value?.kind !== CachedRouteKind.APP_ROUTE) {\n        throw new Error(\n          `Invariant: app-route received invalid cache entry ${cacheEntry?.value?.kind}`\n        )\n      }\n\n      if (!getRequestMeta(req, 'minimalMode')) {\n        res.setHeader(\n          'x-nextjs-cache',\n          isOnDemandRevalidate\n            ? 'REVALIDATED'\n            : cacheEntry.isMiss\n              ? 'MISS'\n              : cacheEntry.isStale\n                ? 'STALE'\n                : 'HIT'\n        )\n      }\n\n      // Draft mode should never be cached\n      if (isDraftMode) {\n        res.setHeader(\n          'Cache-Control',\n          'private, no-cache, no-store, max-age=0, must-revalidate'\n        )\n      }\n\n      const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers)\n\n      if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n        headers.delete(NEXT_CACHE_TAGS_HEADER)\n      }\n\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (\n        cacheEntry.cacheControl &&\n        !res.getHeader('Cache-Control') &&\n        !headers.get('Cache-Control')\n      ) {\n        headers.set(\n          'Cache-Control',\n          getCacheControlHeader(cacheEntry.cacheControl)\n        )\n      }\n\n      await sendResponse(\n        nodeNextReq,\n        nodeNextRes,\n        new Response(cacheEntry.value.body, {\n          headers,\n          status: cacheEntry.value.status || 200,\n        })\n      )\n      return null\n    }\n\n    // TODO: activeSpan code path is for when wrapped by\n    // next-server can be removed when this is no longer used\n    if (activeSpan) {\n      await handleResponse(activeSpan)\n    } else {\n      await tracer.withPropagatedContext(req.headers, () =>\n        tracer.trace(\n          BaseServerSpan.handleRequest,\n          {\n            spanName: `${method} ${req.url}`,\n            kind: SpanKind.SERVER,\n            attributes: {\n              'http.method': method,\n              'http.target': req.url,\n            },\n          },\n          handleResponse\n        )\n      )\n    }\n  } catch (err) {\n    // if we aren't wrapped by base-server handle here\n    if (!activeSpan) {\n      await routeModule.onRequestError(req, err, {\n        routerKind: 'App Router',\n        routePath: normalizedSrcPage,\n        routeType: 'route',\n        revalidateReason: getRevalidateReason({\n          isRevalidate,\n          isOnDemandRevalidate,\n        }),\n      })\n    }\n\n    // rethrow so that we can handle serving error page\n\n    // If this is during static generation, throw the error again.\n    if (isIsr) throw err\n\n    // Otherwise, send a 500 response.\n    await sendResponse(\n      nodeNextReq,\n      nodeNextRes,\n      new Response(null, { status: 500 })\n    )\n    return null\n  }\n}\n"], "names": ["handler", "patchFetch", "routeModule", "serverHooks", "workAsyncStorage", "workUnitAsyncStorage", "AppRouteRouteModule", "definition", "kind", "RouteKind", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "distDir", "process", "env", "__NEXT_RELATIVE_DIST_DIR", "projectDir", "__NEXT_RELATIVE_PROJECT_DIR", "resolvedPagePath", "nextConfigOutput", "userland", "_patchFetch", "req", "res", "ctx", "nextConfig", "srcPage", "TURBOPACK", "replace", "multiZoneDraftMode", "__NEXT_MULTI_ZONE_DRAFT_MODE", "prepareResult", "prepare", "statusCode", "end", "waitUntil", "Promise", "resolve", "buildId", "params", "isDraftMode", "prerenderManifest", "routerServerContext", "isOnDemandRevalidate", "revalidateOnlyGenerated", "resolvedPathname", "normalizedSrcPage", "normalizeAppPath", "isIsr", "Boolean", "dynamicRoutes", "routes", "isP<PERSON>endered", "prerenderInfo", "fallback", "NoFallbackError", "cache<PERSON>ey", "isDev", "supportsDynamicResponse", "isRevalidate", "method", "tracer", "getTracer", "activeSpan", "getActiveScopeSpan", "context", "renderOpts", "experimental", "dynamicIO", "authInterrupts", "incrementalCache", "getRequestMeta", "cacheLifeProfiles", "cacheLife", "onClose", "cb", "on", "onAfterTaskError", "undefined", "onInstrumentationRequestError", "error", "_request", "errorContext", "onRequestError", "sharedContext", "nodeNextReq", "NodeNextRequest", "nodeNextRes", "NodeNextResponse", "nextReq", "NextRequestAdapter", "fromNodeNextRequest", "signalFromNodeResponse", "invokeRouteModule", "span", "handle", "finally", "setAttributes", "rootSpanAttributes", "getRootSpanAttributes", "get", "BaseServerSpan", "handleRequest", "console", "warn", "route", "name", "updateName", "url", "handleResponse", "currentSpan", "cacheEntry", "responseGenerator", "previousCacheEntry", "<PERSON><PERSON><PERSON><PERSON>", "response", "fetchMetrics", "pendingWaitUntil", "cacheTags", "collectedTags", "blob", "headers", "toNodeOutgoingHttpHeaders", "NEXT_CACHE_TAGS_HEADER", "type", "revalidate", "collectedRevalidate", "INFINITE_CACHE", "expire", "collectedExpire", "value", "CachedRouteKind", "status", "body", "<PERSON><PERSON><PERSON>", "from", "arrayBuffer", "cacheControl", "sendResponse", "err", "isStale", "routerKind", "routePath", "routeType", "revalidateReason", "getRevalidateReason", "routeKind", "<PERSON><PERSON><PERSON><PERSON>", "isRoutePPREnabled", "Error", "isMiss", "fromNodeOutgoingHttpHeaders", "delete", "<PERSON><PERSON><PERSON><PERSON>", "set", "getCacheControlHeader", "Response", "withPropagatedContext", "trace", "spanName", "SpanKind", "SERVER", "attributes"], "mappings": ";;;;;;;;;;;;;;;;;;;IA8EsBA,OAAO;eAAPA;;IAHpBC,UAAU;eAAVA;;IAJAC,WAAW;eAAXA;;IAGAC,WAAW;eAAXA;;IAFAC,gBAAgB;eAAhBA;;IACAC,oBAAoB;eAApBA;;;gCArEK;2BACmB;4BACgB;6BAEX;wBACgB;0BACd;sBACiB;6BAI3C;2BACwB;uBACK;8BACP;wBAItB;8BAC+B;4BACiB;yCACvB;+BAKzB;sEAEmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B,2EAA2E;AAC3E,UAAU;AACV,0BAA0B;AAE1B,MAAMH,cAAc,IAAII,mCAAmB,CAAC;IAC1CC,YAAY;QACVC,MAAMC,oBAAS,CAACC,SAAS;QACzBC,MAAM;QACNC,UAAU;QACVC,UAAU;QACVC,YAAY;IACd;IACAC,SAASC,QAAQC,GAAG,CAACC,wBAAwB,IAAI;IACjDC,YAAYH,QAAQC,GAAG,CAACG,2BAA2B,IAAI;IACvDC,kBAAkB;IAClBC;IACAC,UAAAA;AACF;AAEA,2EAA2E;AAC3E,2EAA2E;AAC3E,mCAAmC;AACnC,MAAM,EAAEnB,gBAAgB,EAAEC,oBAAoB,EAAEF,WAAW,EAAE,GAAGD;AAEhE,SAASD;IACP,OAAOuB,IAAAA,sBAAW,EAAC;QACjBpB;QACAC;IACF;AACF;AAUO,eAAeL,QACpByB,GAAoB,EACpBC,GAAmB,EACnBC,GAEC;QA6FsBC;IA3FvB,IAAIC,UAAU;IAEd,wDAAwD;IACxD,mDAAmD;IACnD,6DAA6D;IAC7D,IAAIb,QAAQC,GAAG,CAACa,SAAS,EAAE;QACzBD,UAAUA,QAAQE,OAAO,CAAC,YAAY,OAAO;IAC/C,OAAO,IAAIF,YAAY,UAAU;QAC/B,0CAA0C;QAC1CA,UAAU;IACZ;IACA,MAAMG,qBAAqBhB,QAAQC,GAAG,CACnCgB,4BAA4B;IAE/B,MAAMC,gBAAgB,MAAMhC,YAAYiC,OAAO,CAACV,KAAKC,KAAK;QACxDG;QACAG;IACF;IAEA,IAAI,CAACE,eAAe;QAClBR,IAAIU,UAAU,GAAG;QACjBV,IAAIW,GAAG,CAAC;QACRV,IAAIW,SAAS,oBAAbX,IAAIW,SAAS,MAAbX,KAAgBY,QAAQC,OAAO;QAC/B,OAAO;IACT;IAEA,MAAM,EACJC,OAAO,EACPC,MAAM,EACNd,UAAU,EACVe,WAAW,EACXC,iBAAiB,EACjBC,mBAAmB,EACnBC,oBAAoB,EACpBC,uBAAuB,EACvBC,gBAAgB,EACjB,GAAGd;IAEJ,MAAMe,oBAAoBC,IAAAA,0BAAgB,EAACrB;IAE3C,IAAIsB,QAAQC,QACVR,kBAAkBS,aAAa,CAACJ,kBAAkB,IAChDL,kBAAkBU,MAAM,CAACN,iBAAiB;IAG9C,IAAIG,SAAS,CAACR,aAAa;QACzB,MAAMY,gBAAgBH,QAAQR,kBAAkBU,MAAM,CAACN,iBAAiB;QACxE,MAAMQ,gBAAgBZ,kBAAkBS,aAAa,CAACJ,kBAAkB;QAExE,IAAIO,eAAe;YACjB,IAAIA,cAAcC,QAAQ,KAAK,SAAS,CAACF,eAAe;gBACtD,MAAM,IAAIG,wCAAe;YAC3B;QACF;IACF;IAEA,IAAIC,WAA0B;IAE9B,IAAIR,SAAS,CAACjD,YAAY0D,KAAK,IAAI,CAACjB,aAAa;QAC/CgB,WAAWX;QACX,+CAA+C;QAC/CW,WAAWA,aAAa,WAAW,MAAMA;IAC3C;IAEA,MAAME,0BACJ,0DAA0D;IAC1D3D,YAAY0D,KAAK,KAAK,QACtB,qEAAqE;IACrE,gBAAgB;IAChB,CAACT;IAEH,gEAAgE;IAChE,+DAA+D;IAC/D,4DAA4D;IAC5D,WAAW;IACX,MAAMW,eAAeX,SAAS,CAACU;IAE/B,MAAME,SAAStC,IAAIsC,MAAM,IAAI;IAC7B,MAAMC,SAASC,IAAAA,iBAAS;IACxB,MAAMC,aAAaF,OAAOG,kBAAkB;IAE5C,MAAMC,UAAuC;QAC3C1B;QACAE;QACAyB,YAAY;YACVC,cAAc;gBACZC,WAAWnB,QAAQxB,WAAW0C,YAAY,CAACC,SAAS;gBACpDC,gBAAgBpB,QAAQxB,WAAW0C,YAAY,CAACE,cAAc;YAChE;YACAX;YACAY,kBAAkBC,IAAAA,2BAAc,EAACjD,KAAK;YACtCkD,iBAAiB,GAAE/C,2BAAAA,WAAW0C,YAAY,qBAAvB1C,yBAAyBgD,SAAS;YACrDd;YACAxB,WAAWX,IAAIW,SAAS;YACxBuC,SAAS,CAACC;gBACRpD,IAAIqD,EAAE,CAAC,SAASD;YAClB;YACAE,kBAAkBC;YAClBC,+BAA+B,CAACC,OAAOC,UAAUC,eAC/CnF,YAAYoF,cAAc,CACxB7D,KACA0D,OACAE,cACAxC;QAEN;QACA0C,eAAe;YACb9C;QACF;IACF;IACA,MAAM+C,cAAc,IAAIC,qBAAe,CAAChE;IACxC,MAAMiE,cAAc,IAAIC,sBAAgB,CAACjE;IAEzC,MAAMkE,UAAUC,+BAAkB,CAACC,mBAAmB,CACpDN,aACAO,IAAAA,mCAAsB,EAACrE;IAGzB,IAAI;QACF,MAAMsE,oBAAoB,OAAOC;YAC/B,OAAO/F,YAAYgG,MAAM,CAACN,SAASxB,SAAS+B,OAAO,CAAC;gBAClD,IAAI,CAACF,MAAM;gBAEXA,KAAKG,aAAa,CAAC;oBACjB,oBAAoB1E,IAAIU,UAAU;oBAClC,YAAY;gBACd;gBAEA,MAAMiE,qBAAqBrC,OAAOsC,qBAAqB;gBACvD,iEAAiE;gBACjE,IAAI,CAACD,oBAAoB;oBACvB;gBACF;gBAEA,IACEA,mBAAmBE,GAAG,CAAC,sBACvBC,yBAAc,CAACC,aAAa,EAC5B;oBACAC,QAAQC,IAAI,CACV,CAAC,2BAA2B,EAAEN,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;oBAE1E;gBACF;gBAEA,MAAMK,QAAQP,mBAAmBE,GAAG,CAAC;gBACrC,IAAIK,OAAO;oBACT,MAAMC,OAAO,GAAG9C,OAAO,CAAC,EAAE6C,OAAO;oBAEjCX,KAAKG,aAAa,CAAC;wBACjB,cAAcQ;wBACd,cAAcA;wBACd,kBAAkBC;oBACpB;oBACAZ,KAAKa,UAAU,CAACD;gBAClB,OAAO;oBACLZ,KAAKa,UAAU,CAAC,GAAG/C,OAAO,CAAC,EAAEtC,IAAIsF,GAAG,EAAE;gBACxC;YACF;QACF;QAEA,MAAMC,iBAAiB,OAAOC;gBA6HxBC;YA5HJ,MAAMC,oBAAuC,OAAO,EAClDC,kBAAkB,EACnB;gBACC,IAAI;oBACF,IACE,CAAC1C,IAAAA,2BAAc,EAACjD,KAAK,kBACrBqB,wBACAC,2BACA,CAACqE,oBACD;wBACA1F,IAAIU,UAAU,GAAG;wBACjB,+CAA+C;wBAC/CV,IAAI2F,SAAS,CAAC,kBAAkB;wBAChC3F,IAAIW,GAAG,CAAC;wBACR,OAAO;oBACT;oBAEA,MAAMiF,WAAW,MAAMtB,kBAAkBiB;oBAEvCxF,IAAY8F,YAAY,GAAG,AAACnD,QAAQC,UAAU,CAASkD,YAAY;oBACrE,IAAIC,mBAAmBpD,QAAQC,UAAU,CAACmD,gBAAgB;oBAE1D,gDAAgD;oBAChD,qDAAqD;oBACrD,IAAIA,kBAAkB;wBACpB,IAAI7F,IAAIW,SAAS,EAAE;4BACjBX,IAAIW,SAAS,CAACkF;4BACdA,mBAAmBvC;wBACrB;oBACF;oBACA,MAAMwC,YAAYrD,QAAQC,UAAU,CAACqD,aAAa;oBAElD,mEAAmE;oBACnE,oBAAoB;oBACpB,IAAIvE,OAAO;wBACT,MAAMwE,OAAO,MAAML,SAASK,IAAI;wBAEhC,sCAAsC;wBACtC,MAAMC,UAAUC,IAAAA,iCAAyB,EAACP,SAASM,OAAO;wBAE1D,IAAIH,WAAW;4BACbG,OAAO,CAACE,kCAAsB,CAAC,GAAGL;wBACpC;wBAEA,IAAI,CAACG,OAAO,CAAC,eAAe,IAAID,KAAKI,IAAI,EAAE;4BACzCH,OAAO,CAAC,eAAe,GAAGD,KAAKI,IAAI;wBACrC;wBAEA,MAAMC,aACJ,OAAO5D,QAAQC,UAAU,CAAC4D,mBAAmB,KAAK,eAClD7D,QAAQC,UAAU,CAAC4D,mBAAmB,IAAIC,0BAAc,GACpD,QACA9D,QAAQC,UAAU,CAAC4D,mBAAmB;wBAE5C,MAAME,SACJ,OAAO/D,QAAQC,UAAU,CAAC+D,eAAe,KAAK,eAC9ChE,QAAQC,UAAU,CAAC+D,eAAe,IAAIF,0BAAc,GAChDjD,YACAb,QAAQC,UAAU,CAAC+D,eAAe;wBAExC,2CAA2C;wBAC3C,MAAMlB,aAAiC;4BACrCmB,OAAO;gCACL7H,MAAM8H,8BAAe,CAAC5H,SAAS;gCAC/B6H,QAAQjB,SAASiB,MAAM;gCACvBC,MAAMC,OAAOC,IAAI,CAAC,MAAMf,KAAKgB,WAAW;gCACxCf;4BACF;4BACAgB,cAAc;gCAAEZ;gCAAYG;4BAAO;wBACrC;wBAEA,OAAOjB;oBACT,OAAO;wBACL,2CAA2C;wBAC3C,MAAM2B,IAAAA,0BAAY,EAChBrD,aACAE,aACA4B,UACAlD,QAAQC,UAAU,CAACmD,gBAAgB;wBAErC,OAAO;oBACT;gBACF,EAAE,OAAOsB,KAAK;oBACZ,uDAAuD;oBACvD,gDAAgD;oBAChD,IAAI1B,sCAAAA,mBAAoB2B,OAAO,EAAE;wBAC/B,MAAM7I,YAAYoF,cAAc,CAC9B7D,KACAqH,KACA;4BACEE,YAAY;4BACZC,WAAWpH;4BACXqH,WAAW;4BACXC,kBAAkBC,IAAAA,0BAAmB,EAAC;gCACpCtF;gCACAhB;4BACF;wBACF,GACAD;oBAEJ;oBACA,MAAMiG;gBACR;YACF;YAEA,MAAM5B,aAAa,MAAMhH,YAAY8G,cAAc,CAAC;gBAClDvF;gBACAG;gBACA+B;gBACA0F,WAAW5I,oBAAS,CAACC,SAAS;gBAC9B4I,YAAY;gBACZ1G;gBACA2G,mBAAmB;gBACnBzG;gBACAC;gBACAoE;gBACA7E,WAAWX,IAAIW,SAAS;YAC1B;YAEA,uCAAuC;YACvC,IAAI,CAACa,OAAO;gBACV,OAAO;YACT;YAEA,IAAI+D,CAAAA,+BAAAA,oBAAAA,WAAYmB,KAAK,qBAAjBnB,kBAAmB1G,IAAI,MAAK8H,8BAAe,CAAC5H,SAAS,EAAE;oBAEFwG;gBADvD,MAAM,qBAEL,CAFK,IAAIsC,MACR,CAAC,kDAAkD,EAAEtC,+BAAAA,qBAAAA,WAAYmB,KAAK,qBAAjBnB,mBAAmB1G,IAAI,EAAE,GAD1E,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAI,CAACkE,IAAAA,2BAAc,EAACjD,KAAK,gBAAgB;gBACvCC,IAAI2F,SAAS,CACX,kBACAvE,uBACI,gBACAoE,WAAWuC,MAAM,GACf,SACAvC,WAAW6B,OAAO,GAChB,UACA;YAEZ;YAEA,oCAAoC;YACpC,IAAIpG,aAAa;gBACfjB,IAAI2F,SAAS,CACX,iBACA;YAEJ;YAEA,MAAMO,UAAU8B,IAAAA,mCAA2B,EAACxC,WAAWmB,KAAK,CAACT,OAAO;YAEpE,IAAI,CAAElD,CAAAA,IAAAA,2BAAc,EAACjD,KAAK,kBAAkB0B,KAAI,GAAI;gBAClDyE,QAAQ+B,MAAM,CAAC7B,kCAAsB;YACvC;YAEA,2DAA2D;YAC3D,6DAA6D;YAC7D,IACEZ,WAAW0B,YAAY,IACvB,CAAClH,IAAIkI,SAAS,CAAC,oBACf,CAAChC,QAAQrB,GAAG,CAAC,kBACb;gBACAqB,QAAQiC,GAAG,CACT,iBACAC,IAAAA,mCAAqB,EAAC5C,WAAW0B,YAAY;YAEjD;YAEA,MAAMC,IAAAA,0BAAY,EAChBrD,aACAE,aACA,IAAIqE,SAAS7C,WAAWmB,KAAK,CAACG,IAAI,EAAE;gBAClCZ;gBACAW,QAAQrB,WAAWmB,KAAK,CAACE,MAAM,IAAI;YACrC;YAEF,OAAO;QACT;QAEA,oDAAoD;QACpD,yDAAyD;QACzD,IAAIrE,YAAY;YACd,MAAM8C,eAAe9C;QACvB,OAAO;YACL,MAAMF,OAAOgG,qBAAqB,CAACvI,IAAImG,OAAO,EAAE,IAC9C5D,OAAOiG,KAAK,CACVzD,yBAAc,CAACC,aAAa,EAC5B;oBACEyD,UAAU,GAAGnG,OAAO,CAAC,EAAEtC,IAAIsF,GAAG,EAAE;oBAChCvG,MAAM2J,gBAAQ,CAACC,MAAM;oBACrBC,YAAY;wBACV,eAAetG;wBACf,eAAetC,IAAIsF,GAAG;oBACxB;gBACF,GACAC;QAGN;IACF,EAAE,OAAO8B,KAAK;QACZ,kDAAkD;QAClD,IAAI,CAAC5E,YAAY;YACf,MAAMhE,YAAYoF,cAAc,CAAC7D,KAAKqH,KAAK;gBACzCE,YAAY;gBACZC,WAAWhG;gBACXiG,WAAW;gBACXC,kBAAkBC,IAAAA,0BAAmB,EAAC;oBACpCtF;oBACAhB;gBACF;YACF;QACF;QAEA,mDAAmD;QAEnD,8DAA8D;QAC9D,IAAIK,OAAO,MAAM2F;QAEjB,kCAAkC;QAClC,MAAMD,IAAAA,0BAAY,EAChBrD,aACAE,aACA,IAAIqE,SAAS,MAAM;YAAExB,QAAQ;QAAI;QAEnC,OAAO;IACT;AACF", "ignoreList": [0]}